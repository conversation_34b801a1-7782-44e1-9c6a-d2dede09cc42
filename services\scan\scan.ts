import apiClient from '@/lib/axios';
import { useMutation, useInfiniteQuery, type UseMutationOptions } from '@tanstack/react-query';
import { ScanRequest, ScanResponse, ScanHistoryResponse } from './types';

export const useScan = (
  options?: Omit<UseMutationOptions<ScanResponse, Error, ScanRequest>, 'mutationFn'>
) =>
  useMutation<ScanResponse, Error, ScanRequest>({
    mutationFn: async (data: ScanRequest) => {
      console.log('📤 Uploading compressed image for food analysis...', {
        uri: data.imageUri.substring(0, 50) + '...',
        type: 'image/jpeg',
      });

      const formData = new FormData();

      // Create file object for multipart upload
      formData.append('image', {
        uri: data.imageUri,
        type: 'image/jpeg',
        name: 'food-scan.jpg',
      } as any);

      const startTime = Date.now();
      const response = await apiClient.post('/api/v1/scan/food/analyze/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      const endTime = Date.now();

      console.log('✅ Food analysis completed', {
        uploadDuration: `${endTime - startTime}ms`,
        responseSize: JSON.stringify(response.data).length,
      });

      return response.data;
    },
    ...options,
  });

export const useScanHistory = (options?: any) =>
  useInfiniteQuery({
    queryKey: ['scan-history'],
    queryFn: async ({ pageParam }: { pageParam?: string }) => {
      const url = pageParam || '/api/v1/scan/history/';
      const response = await apiClient.get(url);
      return response.data as ScanHistoryResponse;
    },
    getNextPageParam: (lastPage: ScanHistoryResponse) => {
      // Extract the next page URL from the response
      if (lastPage.next) {
        // Extract just the path and query params from the full URL
        const url = new URL(lastPage.next);
        return url.pathname + url.search;
      }
      return undefined;
    },
    initialPageParam: undefined,
    ...options,
  });
