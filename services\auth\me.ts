import apiClient from '@/lib/axios';
import { QueryOptions, useQuery } from '@tanstack/react-query';
import { OnboardedUser } from './types';

const getMe = async () => {
  const res = await apiClient.get('/api/v1/user/me/');
  return res.data;
};

const useGetMe = (options: QueryOptions<OnboardedUser, Error>) =>
  useQuery({
    queryFn: getMe,
    queryKey: ['me'],
    ...options,
  });

export { useGetMe };
