/**
 * Test utilities for image compression functionality
 * Use these functions to validate compression is working correctly
 */

import { compressImage, compressImagePreset, validateImageUri, CompressionOptions } from './image-compression';

/**
 * Test compression with a sample image URI
 */
export const testImageCompression = async (imageUri: string): Promise<void> => {
  console.log('🧪 Testing image compression...');
  
  try {
    // Test URI validation
    console.log('1️⃣ Testing URI validation...');
    const isValid = validateImageUri(imageUri);
    console.log(`   URI valid: ${isValid}`);
    
    if (!isValid) {
      console.error('❌ Invalid image URI provided');
      return;
    }
    
    // Test food scan preset
    console.log('2️⃣ Testing food scan compression...');
    const foodScanResult = await compressImagePreset.foodScan(imageUri);
    console.log('   Food scan result:', {
      originalUri: imageUri.substring(0, 50) + '...',
      compressedUri: foodScanResult.uri.substring(0, 50) + '...',
      dimensions: foodScanResult.finalDimensions,
    });
    
    // Test thumbnail preset
    console.log('3️⃣ Testing thumbnail compression...');
    const thumbnailResult = await compressImagePreset.thumbnail(imageUri);
    console.log('   Thumbnail result:', {
      dimensions: thumbnailResult.finalDimensions,
    });
    
    // Test custom compression
    console.log('4️⃣ Testing custom compression...');
    const customOptions: CompressionOptions = {
      maxWidth: 600,
      maxHeight: 600,
      quality: 0.7,
    };
    const customResult = await compressImage(imageUri, customOptions);
    console.log('   Custom result:', {
      dimensions: customResult.finalDimensions,
    });
    
    console.log('✅ All compression tests passed!');
  } catch (error) {
    console.error('❌ Compression test failed:', error);
    throw error;
  }
};

/**
 * Benchmark compression performance
 */
export const benchmarkCompression = async (imageUri: string): Promise<void> => {
  console.log('⏱️ Benchmarking compression performance...');
  
  const presets = [
    { name: 'Food Scan', fn: compressImagePreset.foodScan },
    { name: 'Thumbnail', fn: compressImagePreset.thumbnail },
    { name: 'Preview', fn: compressImagePreset.preview },
    { name: 'Balanced', fn: compressImagePreset.balanced },
  ];
  
  for (const preset of presets) {
    const startTime = Date.now();
    try {
      const result = await preset.fn(imageUri);
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`📊 ${preset.name}:`, {
        duration: `${duration}ms`,
        dimensions: result.finalDimensions,
        uri: result.uri.substring(0, 30) + '...',
      });
    } catch (error) {
      console.error(`❌ ${preset.name} failed:`, error);
    }
  }
};

/**
 * Test compression with different quality settings
 */
export const testQualityLevels = async (imageUri: string): Promise<void> => {
  console.log('🎚️ Testing different quality levels...');
  
  const qualityLevels = [0.5, 0.7, 0.8, 0.9, 1.0];
  
  for (const quality of qualityLevels) {
    try {
      const startTime = Date.now();
      const result = await compressImage(imageUri, {
        maxWidth: 800,
        maxHeight: 800,
        quality,
      });
      const endTime = Date.now();
      
      console.log(`📈 Quality ${quality}:`, {
        duration: `${endTime - startTime}ms`,
        dimensions: result.finalDimensions,
      });
    } catch (error) {
      console.error(`❌ Quality ${quality} failed:`, error);
    }
  }
};

/**
 * Validate that compression actually reduces file size (when possible)
 */
export const validateCompressionEffectiveness = async (imageUri: string): Promise<boolean> => {
  console.log('🔍 Validating compression effectiveness...');
  
  try {
    // Test with aggressive compression
    const aggressiveResult = await compressImage(imageUri, {
      maxWidth: 512,
      maxHeight: 512,
      quality: 0.6,
    });
    
    // Test with light compression
    const lightResult = await compressImage(imageUri, {
      maxWidth: 1024,
      maxHeight: 1024,
      quality: 0.9,
    });
    
    console.log('📊 Compression comparison:', {
      original: imageUri.substring(0, 30) + '...',
      aggressive: {
        uri: aggressiveResult.uri.substring(0, 30) + '...',
        dimensions: aggressiveResult.finalDimensions,
      },
      light: {
        uri: lightResult.uri.substring(0, 30) + '...',
        dimensions: lightResult.finalDimensions,
      },
    });
    
    // Check if dimensions are actually reduced
    const aggressiveDims = aggressiveResult.finalDimensions;
    const lightDims = lightResult.finalDimensions;
    
    const isEffective = 
      aggressiveDims && lightDims &&
      (aggressiveDims.width < lightDims.width || aggressiveDims.height < lightDims.height);
    
    console.log(`✅ Compression is ${isEffective ? 'effective' : 'not effective'}`);
    return isEffective;
  } catch (error) {
    console.error('❌ Validation failed:', error);
    return false;
  }
};

/**
 * Complete test suite for image compression
 */
export const runCompressionTestSuite = async (imageUri: string): Promise<void> => {
  console.log('🚀 Running complete image compression test suite...');
  console.log('=' .repeat(50));
  
  try {
    await testImageCompression(imageUri);
    console.log('');
    
    await benchmarkCompression(imageUri);
    console.log('');
    
    await testQualityLevels(imageUri);
    console.log('');
    
    const isEffective = await validateCompressionEffectiveness(imageUri);
    console.log('');
    
    console.log('🎉 Test suite completed successfully!');
    console.log(`📊 Overall result: Compression is ${isEffective ? 'working correctly' : 'needs review'}`);
  } catch (error) {
    console.error('💥 Test suite failed:', error);
    throw error;
  }
};
