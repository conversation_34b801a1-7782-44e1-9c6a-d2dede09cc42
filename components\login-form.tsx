import Ionicons from '@expo/vector-icons/Ionicons';
import { zodResolver } from '@hookform/resolvers/zod';
import { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { TextInput, View } from 'react-native';
import * as z from 'zod';
import { AuthFormProps } from './types';
import CustomButton from './ui/button';
import { CustomText } from './ui/text';
import { router } from 'expo-router';

const formSchema = z.object({
  email: z.email('Please enter a valid email address.'),
  password: z.string().min(8, 'Password must be at least 8 characters long.'),
});

type FormSchema = z.infer<typeof formSchema>;

const LoginForm = ({ OnAuthTypeChange }: AuthFormProps) => {
  const [showPassword, setShowPassword] = useState(false);
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit = (data: FormSchema) => {
    console.log(data);
    router.replace('/onboarding-1');
  };

  return (
    <View className="flex w-full flex-col items-center justify-center gap-2">
      <Controller
        control={control}
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            className="h-12 w-full rounded-2xl bg-white placeholder:pl-5 placeholder:font-lexend-light placeholder:text-gray-400"
            placeholder="Enter Email"
            onBlur={onBlur}
            onChangeText={onChange}
            value={value}
          />
        )}
        name="email"
      />
      {errors.email && <CustomText variant={'error'}>{errors.email.message}</CustomText>}

      <View className="flex w-full flex-row items-center justify-center rounded-2xl bg-white pr-2.5">
        <Controller
          control={control}
          render={({ field: { onChange, onBlur, value } }) => (
            <TextInput
              className="h-12 flex-1 placeholder:pl-5 placeholder:font-lexend-light placeholder:text-gray-400"
              placeholder="Enter Password"
              onBlur={onBlur}
              onChangeText={onChange}
              value={value}
              secureTextEntry={!showPassword}
            />
          )}
          name="password"
        />
        <CustomButton
          className="w-fit bg-transparent p-2.5"
          onPress={() => setShowPassword((e) => !e)}>
          {showPassword ? (
            <Ionicons name="eye" size={24} color="#e29478eb" />
          ) : (
            <Ionicons name="eye-off-outline" size={24} color="#e29478eb" />
          )}
        </CustomButton>
      </View>
      {errors.password && <CustomText variant={'error'}>{errors.password.message}</CustomText>}

      <CustomButton className="mt-2" onPress={handleSubmit(onSubmit)}>
        <CustomText className="font-alansans text-primary-foreground">Login</CustomText>
      </CustomButton>

      <View className="flex w-full flex-row items-center justify-center">
        <View className="flex-1 border border-muted" />
        <CustomText className="px-2 ">OR</CustomText>
        <View className="flex-1 border border-muted" />
      </View>

      <CustomButton
        buttonVariant="muted"
        className="flex h-12 w-full flex-row items-center justify-center gap-4">
        <Ionicons name="logo-google" size={24} color="#e29478eb" />
        <CustomText className="font-alansans">Connect with Google</CustomText>
      </CustomButton>

      <View className="mt-3 flex flex-row items-center justify-center">
        <CustomText className="font-lexend-light">Don&apos;t have an account? </CustomText>
        <CustomText
          variant="link"
          onPress={() => OnAuthTypeChange('signup')}
          className="font-lexend-light">
          Register Now
        </CustomText>
      </View>
    </View>
  );
};

export default LoginForm;
