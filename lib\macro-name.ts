import { Macros } from '@/services/scan/types';

export const macroName = (key: string) => {
  // Remove '_g' suffix
  let name = key.replace(/_g$/, '');
  // Replace underscores with spaces
  name = name.replace(/_/g, ' ');
  // Capitalize each word
  name = name.replace(/\b\w/g, (char) => char.toUpperCase());
  // Replace "Fat" with "Fats"
  name = name.replace(/\bFat\b/g, 'Fats');
  return name;
};

export const getMacrosArray = (data: Macros) =>
  Object.entries(data)
    .filter(([, value]) => value && value > 0)
    .map(([key, value], index) => ({
      id: index,
      key: key as keyof Macros,
      name: macroName(key),
      value: value,
    }));
