import { BadgeDetailedProps, BadgeProps } from '@/components/badges';
import { cn } from '@/lib/utils';
import { Ionicons } from '@expo/vector-icons';

export const badges: BadgeProps[] = [
  {
    name: 'Badge1',
    isUnlocked: true,
    icon: ({ color, className, size }) => (
      <Ionicons
        name="flame-outline"
        className={cn('rounded-full bg-muted p-2', className)}
        size={size}
        color={color}
      />
    ),
  },
  {
    name: 'Badge1',
    isUnlocked: true,
    icon: ({ color, className, size }) => (
      <Ionicons
        name="fast-food-outline"
        className={cn('rounded-full bg-muted p-2', className)}
        size={size}
        color={color}
      />
    ),
  },
  {
    name: 'Badge1',
    isUnlocked: true,
    icon: ({ color, className, size }) => (
      <Ionicons
        name="flame-outline"
        className={cn('rounded-full bg-muted p-2', className)}
        size={size}
        color={color}
      />
    ),
  },
  {
    name: 'Badge1',
    isUnlocked: true,
    icon: ({ color, className, size }) => (
      <Ionicons
        name="flame-outline"
        className={cn('rounded-full bg-muted p-2', className)}
        size={size}
        color={color}
      />
    ),
  },
  {
    name: 'Badge1',
    isUnlocked: true,
    icon: ({ color, className, size }) => (
      <Ionicons
        name="flame-outline"
        className={cn('rounded-full bg-muted p-2', className)}
        size={size}
        color={color}
      />
    ),
  },
  {
    name: 'Badge1',
    isUnlocked: true,
    icon: ({ color, className, size }) => (
      <Ionicons
        name="flame-outline"
        className={cn('rounded-full bg-muted p-2', className)}
        size={size}
        color={color}
      />
    ),
  },
  {
    name: 'Badge1',
    isUnlocked: false,
    icon: ({ color, className, size }) => (
      <Ionicons
        name="flame-outline"
        className={cn('rounded-full bg-muted p-2', className)}
        size={size}
        color={color}
      />
    ),
  },
  {
    name: 'Badge1',
    isUnlocked: false,
    icon: ({ color, className, size }) => (
      <Ionicons
        name="flame-outline"
        className={cn('rounded-full bg-muted p-2', className)}
        size={size}
        color={color}
      />
    ),
  },
];

export const badgesDetailed: BadgeDetailedProps[] = badges.map((item) => ({
  ...item,
  description: 'This is the description of the badge',
  icon({ className, size }) {
    return (
      <Ionicons
        name="flame-outline"
        className={cn('rounded-full bg-muted p-2', className)}
        size={size}
        color={'#e29578'}
      />
    );
  },
}));
