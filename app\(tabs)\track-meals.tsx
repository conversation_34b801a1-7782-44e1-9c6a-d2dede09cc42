import { router } from 'expo-router';
import { MealCard } from '@/components/track-meals/history-meal-card';
import { MealCardProps } from '@/components/track-meals/types';
import SafeAreaContainer from '@/components/ui/safe-area-container';
import { SegmentedControl } from '@/components/ui/segmented-control';
import { CustomText } from '@/components/ui/text';
import { useMemo, useState, useCallback } from 'react';
import { SectionList, View, ActivityIndicator, RefreshControl } from 'react-native';
import { useScanHistory } from '@/services/scan/scan';
import { transformScanHistoryToMealCard } from '@/lib/scan-history-utils';
import { useScanData } from '@/stores/scanStore';
import { getMacrosArray } from '@/lib/macro-name';

interface MealWithTracking extends MealCardProps {
  isUntracked: boolean;
  originalItem?: any; // ScanHistoryItem for untracked meals
}

interface GroupedMealData {
  title: string;
  data: MealWithTracking[];
  totalCalories: number;
}

const groupMealsByDate = (meals: MealWithTracking[]): GroupedMealData[] => {
  const groupedMeals = meals.reduce(
    (acc, meal) => {
      const date = new Date(meal.timestamp).toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      if (!acc[date]) {
        acc[date] = [];
      }
      acc[date].push(meal);
      return acc;
    },
    {} as Record<string, MealWithTracking[]>
  );

  return Object.keys(groupedMeals).map((date) => {
    const mealsForDate = groupedMeals[date];
    const totalCalories = mealsForDate.reduce((sum, meal) => {
      const calorieValue = parseInt(meal.calories.toString());
      return sum + (isNaN(calorieValue) ? 0 : calorieValue);
    }, 0);

    return {
      title: date,
      data: mealsForDate,
      totalCalories,
    };
  });
};

const TrackMeals = () => {
  const [filterType, setFilterType] = useState<'All' | 'Tracked' | 'Untracked'>('All');
  const { setScanData } = useScanData();

  // Fetch scan history with infinite scrolling
  const {
    data: scanHistoryData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    refetch,
    isRefetching,
  } = useScanHistory();

  // Transform scan history data to meal cards
  const allMeals = useMemo(() => {
    if (!scanHistoryData?.pages) return [];

    return scanHistoryData.pages.flatMap((page: any) =>
      page.results.map((item: any) => transformScanHistoryToMealCard(item))
    );
  }, [scanHistoryData]);

  // Handle infinite scrolling
  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    refetch();
  }, [refetch]);

  // Group all meals by date
  const allGroupedData = useMemo(() => {
    return groupMealsByDate(allMeals);
  }, [allMeals]);

  // Filter data based on selected filter
  const filteredGroupedData = useMemo(() => {
    return allGroupedData
      .map((section) => ({
        ...section,
        data: section.data.filter((meal) => {
          if (filterType === 'All') return true;
          if (filterType === 'Tracked') return !meal.isUntracked;
          if (filterType === 'Untracked') return meal.isUntracked;
          return true;
        }),
      }))
      .filter((section) => section.data.length > 0);
  }, [filterType, allGroupedData]);

  const handleAddMeal = useCallback(
    (meal: MealWithTracking) => {
      // Use the stored original item reference to avoid expensive lookups
      const originalItem = meal.originalItem;

      if (!originalItem) {
        console.warn('No original item found for untracked meal');
        return;
      }

      // Convert macros to the expected format
      const macrosArray = getMacrosArray(originalItem.nutrition_label.macros);

      // Set the scan data in the store
      const scanData = {
        id: originalItem.nutrition_label.id,
        food_name: originalItem.nutrition_label.food_name,
        brand: originalItem.nutrition_label.brand,
        serving: originalItem.nutrition_label.serving,
        calories: originalItem.nutrition_label.calories,
        macros: macrosArray,
        source: originalItem.nutrition_label.source,
        is_eatable: originalItem.nutrition_label.is_eatable,
        is_consumed: originalItem.is_consumed,
        advice: originalItem.nutrition_label.advice,
        image_url: originalItem.image_url, // Use backend image URL
        imageURI: meal.uri, // Keep for backward compatibility
      };

      setScanData(scanData);

      // Navigate without URL params
      router.push('/(inner-screens)/scan-results');
    },
    [setScanData]
  );

  const handleTrackedMealPress = useCallback(
    (meal: MealWithTracking) => {
      // Use the stored original item reference
      const originalItem = meal.originalItem;

      if (!originalItem) {
        console.warn('No original item found for tracked meal');
        return;
      }

      const macrosArray = getMacrosArray(originalItem.nutrition_label.macros);

      const scanData = {
        id: originalItem.nutrition_label.id,
        food_name: originalItem.nutrition_label.food_name,
        brand: originalItem.nutrition_label.brand,
        serving: originalItem.nutrition_label.serving,
        calories: originalItem.nutrition_label.calories,
        macros: macrosArray,
        source: originalItem.nutrition_label.source,
        is_eatable: originalItem.nutrition_label.is_eatable,
        is_consumed: originalItem.is_consumed,
        advice: originalItem.nutrition_label.advice,
        image_url: originalItem.image_url, // Use backend image URL
        imageURI: meal.uri, // Keep for backward compatibility
      };

      setScanData(scanData);

      router.push('/(inner-screens)/scan-results');
    },
    [setScanData]
  );

  // Memoized render function to avoid creating new functions on every render
  const renderMealCard = useCallback(
    ({ item }: { item: MealWithTracking }) => (
      <MealCard
        {...item}
        isUntracked={item.isUntracked}
        onAddMeal={item.isUntracked ? () => handleAddMeal(item) : undefined}
        onMealPress={!item.isUntracked ? () => handleTrackedMealPress(item) : undefined}
      />
    ),
    [handleAddMeal, handleTrackedMealPress]
  );

  return (
    <SafeAreaContainer>
      <View className="flex-1">
        <CustomText variant={'h2'} className="mb-1 text-black">
          Meal History
        </CustomText>
        <CustomText className="mb-5 text-muted-foreground">
          Track your daily meals and nutrition
        </CustomText>

        {/* Filter Segmented Control */}
        <View className="mb-2">
          <SegmentedControl
            options={['All', 'Tracked', 'Untracked']}
            value={filterType}
            onValueChange={(value) => setFilterType(value as 'All' | 'Tracked' | 'Untracked')}
          />
        </View>

        {isLoading ? (
          <View className="flex-1 items-center justify-center">
            <ActivityIndicator size="large" color="#006d77" />
            <CustomText className="mt-2 text-muted-foreground">Loading meals...</CustomText>
          </View>
        ) : isError ? (
          <View className="flex-1 items-center justify-center">
            <CustomText className="text-center text-muted-foreground">
              Failed to load meals. Pull to refresh.
            </CustomText>
          </View>
        ) : filteredGroupedData.length === 0 ? (
          <View className="flex-1 items-center justify-center">
            <CustomText className="text-center text-muted-foreground">
              No {filterType === 'All' ? 'meals' : filterType.toLowerCase() + ' meals'} found
            </CustomText>
          </View>
        ) : (
          <SectionList
            sections={filteredGroupedData}
            showsVerticalScrollIndicator={false}
            keyExtractor={(item) =>
              `${item.originalItem?.nutrition_label?.id || item.mealName}-${item.timestamp}`
            }
            renderItem={renderMealCard}
            renderSectionHeader={({ section: { title, totalCalories } }) => (
              <View className="mb-3 mt-8 flex flex-row items-start justify-between gap-x-5">
                <CustomText variant={'h4'} className="flex-1 font-poppins text-lg text-primary">
                  {title}
                </CustomText>
                <View className="rounded-full bg-muted px-3 py-1">
                  <CustomText className="text-sm font-medium text-accent">
                    {totalCalories} Cal
                  </CustomText>
                </View>
              </View>
            )}
            ItemSeparatorComponent={() => <View className="h-3" />}
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.1}
            refreshControl={
              <RefreshControl
                refreshing={isRefetching}
                onRefresh={handleRefresh}
                colors={['#006d77']}
                tintColor="#006d77"
              />
            }
            ListFooterComponent={() =>
              isFetchingNextPage ? (
                <View className="items-center py-4">
                  <ActivityIndicator size="small" color="#006d77" />
                </View>
              ) : null
            }
          />
        )}
      </View>
    </SafeAreaContainer>
  );
};

export default TrackMeals;
