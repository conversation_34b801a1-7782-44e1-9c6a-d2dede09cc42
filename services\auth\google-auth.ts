import { COMMON_TOAST_PROPS } from '@/constants/toast-common';
import apiClient from '@/lib/axios';
import { SecureStorage } from '@/lib/utils';
import { useMutation, type UseMutationOptions } from '@tanstack/react-query';
import { Platform } from 'react-native';
import { showToast } from 'react-native-nitro-toast';
import { GoogleAuthResponse } from './types';

let GoogleSignin: any;
let isErrorWithCode: (error: any) => boolean;
let isSuccessResponse: (response: any) => boolean;
let statusCodes: any;

// DO NOT CHANGE THIS TO IMPORT SYNTAX, DEV SERVER WONT WORK !!
if (Platform.OS === 'android') {
  const googleSigninModule = require('@react-native-google-signin/google-signin');
  GoogleSignin = googleSigninModule.GoogleSignin;
  isErrorWithCode = googleSigninModule.isErrorWithCode;
  isSuccessResponse = googleSigninModule.isSuccessResponse;
  statusCodes = googleSigninModule.statusCodes;
}

const handleGoogleOAuth = async () => {
  try {
    console.log('Checking play services');
    await GoogleSignin.hasPlayServices();
    console.log('Play services available');
    const response = await GoogleSignin.signIn();

    if (isSuccessResponse(response)) {
      console.log('Sign in successful');
      return response.data.idToken;
    } else {
      showToast('User has cancelled login!', {
        type: 'warning',
        title: 'Failed to login!',
        ...COMMON_TOAST_PROPS,
      });
      console.log('Sign in was cancelled');
    }
  } catch (error) {
    const typedError = error as { code: string | number };

    if (isErrorWithCode(error)) {
      switch (typedError.code) {
        case statusCodes.IN_PROGRESS:
          showToast('Sign is already in progress!', {
            type: 'warning',
            title: 'Please wait.',
            ...COMMON_TOAST_PROPS,
          });
          break;
        case statusCodes.PLAY_SERVICES_NOT_AVAILABLE:
          console.log('Play services not available or outdated', typedError);
          break;
        default:
          console.log('Unknown error occurred', typedError);
      }
    } else {
      showToast('Something went wrong! Please try again.', {
        type: 'error',
        title: 'Failed to login2!',
        ...COMMON_TOAST_PROPS,
      });
    }
  }
};

export const handleGoogleSignOut = async () => {
  await GoogleSignin.signOut();
};

const googleAuth = async () => {
  const idToken = await handleGoogleOAuth();
  console.log(idToken, 'id token');
  const response = await apiClient.post<GoogleAuthResponse>('/api/v1/auth/google/', {
    token: idToken,
  });
  await SecureStorage.setAccessToken(response.data.access);
  await SecureStorage.setRefreshToken(response.data.refresh);
  await SecureStorage.setUserProfile(response.data.user);
  return response.data;
};

export const useGoogleAuth = (
  options?: Omit<UseMutationOptions<GoogleAuthResponse, Error, void>, 'mutationFn'>
) =>
  useMutation<GoogleAuthResponse, Error, void>({
    mutationFn: googleAuth,
    ...options,
  });
