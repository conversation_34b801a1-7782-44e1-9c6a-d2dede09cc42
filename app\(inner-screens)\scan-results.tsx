import { EatableInfoBox } from '@/components/scan-and-check/eatable-info';
import { FoodCard } from '@/components/scan-and-check/food-card';
import CustomButton from '@/components/ui/button';
import SafeAreaContainer from '@/components/ui/safe-area-container';
import { CustomText } from '@/components/ui/text';
import { COMMON_TOAST_PROPS } from '@/constants/toast-common';
import { useAddToLog } from '@/services/log/add-to-log';
import { useScanData } from '@/stores/scanStore';
import { Ionicons } from '@expo/vector-icons';
import { Redirect, useRouter } from 'expo-router';
import { useCallback } from 'react';
import { ActivityIndicator, ScrollView, View } from 'react-native';
import { showToast } from 'react-native-nitro-toast';

export default function FoodScanResultScreen() {
  const router = useRouter();
  const { scanData, clearScanData } = useScanData();

  const { mutate, isPending } = useAddToLog({
    onSuccess() {
      showToast('Meal is not logged.', {
        ...COMMON_TOAST_PROPS,
        title: 'Skipped',
        type: 'info',
      });
      clearScanData();
      router.back();
    },
    onError(error) {
      showToast('Something went wrong!' + error.message, {
        ...COMMON_TOAST_PROPS,
        type: 'error',
      });
    },
  });

  const handleYes = useCallback(() => {
    // Navigate directly without URL params - the add-to-log page will use the store
    router.push('/add-to-log');
  }, [router]);

  const handleNo = useCallback(() => {
    mutate({
      nutrition_label: scanData?.id || 0,
      consumed: false,
    });
  }, [mutate, scanData?.id]);

  const handleAddLater = useCallback(() => {
    showToast('Meal is saved. You can add it to your log later.', {
      ...COMMON_TOAST_PROPS,
      title: 'Saved for later',
      type: 'info',
    });
    clearScanData();
    router.back();
  }, [router, clearScanData]);

  // Redirect if no scan data available
  if (!scanData) {
    showToast('No scan data available. Please scan again!', {
      type: 'warning',
      title: 'Missing Data',
      ...COMMON_TOAST_PROPS,
    });
    return <Redirect href={'/scan-and-check'} />;
  }

  return (
    <SafeAreaContainer className="ios:-mt-10 flex-1 pt-0">
      <ScrollView showsVerticalScrollIndicator={false}>
        <FoodCard
          image={scanData.image_url || scanData.imageURI || ''}
          foodName={scanData.food_name?.toString() || 'Unknown Food'}
          calories={scanData.calories?.toString() || '0'}
          macros={scanData.macros || []}
        />

        <EatableInfoBox
          message={scanData.advice || ''}
          title={scanData.is_eatable === 'False' ? 'No' : 'Yes'}
          type={scanData.is_eatable === 'False' ? 'warning' : 'success'}
        />

        {!scanData.is_consumed && (
          <View className="mt-6 w-full rounded-2xl bg-card p-4">
            <CustomText variant="large" className="mb-4 text-center">
              Are you eating this?
            </CustomText>
            <View className="mb-3 flex w-full flex-row items-center justify-center gap-3">
              <CustomButton
                className="w-full flex-1 flex-row items-center gap-2 rounded-full"
                onPress={handleYes}
                disabled={isPending}
                buttonVariant="primary">
                {isPending ? (
                  <ActivityIndicator size="small" color="white" />
                ) : (
                  <Ionicons name="checkmark-outline" color={'white'} size={24} />
                )}
                <CustomText
                  variant="buttonText"
                  className="font-alansans-bold text-primary-foreground">
                  Yes
                </CustomText>
              </CustomButton>

              <CustomButton
                onPress={handleNo}
                disabled={isPending}
                buttonVariant="accent"
                className="flex-1 flex-row items-center gap-2 rounded-full border border-accent bg-transparent">
                {isPending ? (
                  <ActivityIndicator size="small" color="#e29578" />
                ) : (
                  <Ionicons name="close-outline" color={'#e29578'} size={24} />
                )}
                <CustomText variant="buttonText" className="font-alansans-bold text-accent">
                  No
                </CustomText>
              </CustomButton>
            </View>

            <CustomButton
              disabled={isPending}
              onPress={handleAddLater}
              className="rounded-full bg-transparent">
              <CustomText
                variant="buttonText"
                className="font-alansans-bold text-secondary-foreground underline underline-offset-4">
                Add Later
              </CustomText>
            </CustomButton>
          </View>
        )}
      </ScrollView>
    </SafeAreaContainer>
  );
}
