import { cn } from '@/lib/utils';
import { NativeSafeAreaViewProps, SafeAreaView } from 'react-native-safe-area-context';

const SafeAreaContainer = (props: NativeSafeAreaViewProps) => {
  const { children, className, ...otherProps } = props;
  return (
    <SafeAreaView className={cn('flex-1 bg-background px-5 pt-5', className)} {...otherProps}>
      {children}
    </SafeAreaView>
  );
};

export default SafeAreaContainer;
