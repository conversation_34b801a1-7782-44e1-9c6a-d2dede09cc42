import NextButton from '@/components/onboarding/next-button';
import OnboardingBars from '@/components/onboarding/onboarding-bars';
import CustomButton from '@/components/ui/button';
import Form from '@/components/ui/form';
import CustomInput from '@/components/ui/input';
import SafeAreaContainer from '@/components/ui/safe-area-container';
import { CustomText } from '@/components/ui/text';
import { COMMON_TOAST_PROPS } from '@/constants/toast-common';
import { cn } from '@/lib/utils';
import { useOnboarding } from '@/services/auth/onboarding';
import { zodResolver } from '@hookform/resolvers/zod';
import { router } from 'expo-router';
import { Controller, Resolver, useForm } from 'react-hook-form';
import { View } from 'react-native';
import { showToast } from 'react-native-nitro-toast';
import * as z from 'zod';

const formSchema = z.object({
  gender: z.enum(['male', 'female', 'others'], {
    error: 'Please select a gender',
  }),
  age: z.coerce.number({ error: 'Age is required' }).gte(1, 'Please enter a valid age'),
  height: z.coerce.number({ error: 'Height is required' }).gte(1, 'Please enter a valid height'),
  weight: z.coerce.number({ error: 'Weight is required' }).gte(1, 'Please enter a valid weight'),
});

type FormSchema = z.infer<typeof formSchema>;

const OnBoarding = () => {
  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<FormSchema>({
    // this following force typing is required because coerce thing that happens at runtime,
    // we should tell TS that its safe and will conver the values into
    // the correct type
    resolver: zodResolver(formSchema) as unknown as Resolver<FormSchema>,
  });
  const { mutate, isPending } = useOnboarding({
    onSuccess() {
      router.push('/onboarding-3');
    },
    onError() {
      showToast('Error updating info! Please try again later.', {
        ...COMMON_TOAST_PROPS,
        type: 'error',
      });
    },
  });
  const gender = watch('gender');

  const onSubmit = (data: FormSchema) => {
    mutate({
      ...data,
      weight: data.weight.toString(),
      height: data.height.toString(),
    });
  };

  return (
    <SafeAreaContainer className="flex flex-1 items-center justify-start pt-0">
      <Form>
        <View className="max-h-screen-safe flex-1">
          <OnboardingBars barsFilled={2} />
          <View className="gap-y-3">
            <CustomText className="ios:text-5xl ios:pt-3 self-start font-poppins-bold text-2xl text-foreground">
              More about your physique
            </CustomText>
            <CustomText className="self-start font-lexend text-lg text-accent-foreground">
              This helps us personalise the experience for you
            </CustomText>
          </View>

          <View className="my-4 flex w-full flex-col items-center justify-center gap-y-3">
            <CustomText className="self-start text-sm">Please select your gender</CustomText>

            <View className="w-full">
              <View className=" mb-2 flex flex-row items-center justify-center gap-x-2">
                <CustomButton
                  onPress={() => setValue('gender', 'male', { shouldValidate: true })}
                  buttonVariant={gender === 'male' ? 'primary' : 'secondary'}
                  className="flex-1">
                  <CustomText className={cn('font-alansans', gender === 'male' && 'text-white')}>
                    Male
                  </CustomText>
                </CustomButton>
                <CustomButton
                  onPress={() => setValue('gender', 'female', { shouldValidate: true })}
                  buttonVariant={gender === 'female' ? 'primary' : 'secondary'}
                  className="flex-1">
                  <CustomText className={cn('font-alansans', gender === 'female' && 'text-white')}>
                    Female
                  </CustomText>
                </CustomButton>
              </View>
              <CustomButton
                onPress={() => setValue('gender', 'others', { shouldValidate: true })}
                className="w-full"
                buttonVariant={gender === 'others' ? 'primary' : 'secondary'}>
                <CustomText className={cn('font-alansans', gender === 'others' && 'text-white')}>
                  Others
                </CustomText>
              </CustomButton>
            </View>

            {errors.gender && (
              <CustomText className="ml-2" variant={'error'}>
                {errors.gender.message}
              </CustomText>
            )}

            <CustomText className="self-start text-sm">How old are you?</CustomText>
            <Controller
              control={control}
              name="age"
              render={({ field: { onChange, onBlur, value } }) => (
                <CustomInput
                  className="w-full"
                  placeholder="Enter your age"
                  keyboardType="numeric"
                  inputType="primary"
                  onBlur={onBlur}
                  onChangeText={onChange}
                  value={value?.toString() ?? ''}
                />
              )}
            />
            {errors.age && (
              <CustomText className="ml-2 mt-1" variant={'error'}>
                {errors.age.message}
              </CustomText>
            )}

            <CustomText className="self-start text-sm">How tall are you?</CustomText>
            <View className="flex w-full flex-row items-start justify-center gap-4">
              <Controller
                control={control}
                name="height"
                render={({ field: { onChange, onBlur, value } }) => (
                  <CustomInput
                    className="w-full"
                    keyboardType="numeric"
                    inputType="secondary"
                    placeholder="Enter your height in cm"
                    onBlur={onBlur}
                    onChangeText={onChange}
                    value={value?.toString() ?? ''}>
                    <CustomText className="text-zinc-400">cm</CustomText>
                  </CustomInput>
                )}
              />
            </View>
            {errors.height && (
              <CustomText className="ml-2 mt-1" variant={'error'}>
                {errors.height.message}
              </CustomText>
            )}

            <CustomText className="self-start text-sm">How much do you weigh?</CustomText>
            <View className="flex w-full flex-row items-start justify-center gap-4">
              <Controller
                control={control}
                name="weight"
                render={({ field: { onChange, onBlur, value } }) => (
                  <CustomInput
                    className="w-full"
                    placeholder="Enter your weight in kg"
                    keyboardType="numeric"
                    inputType="secondary"
                    onBlur={onBlur}
                    onChangeText={onChange}
                    value={value?.toString() ?? ''}>
                    <CustomText className="text-zinc-400">kg</CustomText>
                  </CustomInput>
                )}
              />
            </View>
            {errors.weight && (
              <CustomText className="ml-2 mt-1" variant={'error'}>
                {errors.weight.message}
              </CustomText>
            )}
          </View>
          <NextButton
            buttonTitle={isPending ? 'Loading...' : 'Next'}
            disabled={isPending}
            onPress={handleSubmit(onSubmit)}
          />
        </View>
      </Form>
    </SafeAreaContainer>
  );
};

export default OnBoarding;
