import { View } from 'react-native';
import ProgressBar from '../ui/progress-bar';
import { CustomText } from '../ui/text';
import { TodaySummaryResponse } from '@/services/scan/types';

const MacroIndicators = ({
  threshold,
  macrosData,
}: {
  threshold: { daily_carbs_g: string; daily_fat_g: string; daily_protein_g: string };
  macrosData: TodaySummaryResponse['macros'];
}) => {
  return (
    <View className="gap-y-3">
      <CustomText variant="h3">Macros</CustomText>

      <View>
        <View className="flex flex-row items-center justify-between">
          <CustomText>Protein</CustomText>
          <CustomText>{`${macrosData.protein_g}g of ${threshold.daily_protein_g}g`}</CustomText>
        </View>
        <ProgressBar
          isBeyond={Number(macrosData.protein_g) > Number(threshold.daily_protein_g)}
          progress={Number(macrosData.protein_g) / Number(threshold.daily_protein_g)}
        />
      </View>

      <View>
        <View className="flex flex-row items-center justify-between">
          <CustomText>Carbs</CustomText>
          <CustomText>{`${macrosData.carbs_g}g of ${threshold.daily_carbs_g}g`}</CustomText>
        </View>
        <ProgressBar
          isBeyond={Number(macrosData.carbs_g) > Number(threshold.daily_carbs_g)}
          progress={Number(macrosData.carbs_g) / Number(threshold.daily_carbs_g)}
        />
      </View>

      <View>
        <View className="flex flex-row items-center justify-between">
          <CustomText>Fats</CustomText>
          <CustomText>{`${macrosData.fat_g}g of ${threshold.daily_fat_g}g`}</CustomText>
        </View>
        <ProgressBar
          isBeyond={Number(macrosData.fat_g) > Number(threshold.daily_fat_g)}
          progress={Number(macrosData.fat_g) / Number(threshold.daily_fat_g)}
        />
      </View>
    </View>
  );
};

export default MacroIndicators;
