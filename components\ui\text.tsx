import { cn } from '@/lib/utils';
import { cva, type VariantProps } from 'class-variance-authority';
import * as React from 'react';
import { Platform, Text as RNText, type Role } from 'react-native';
import { Slot } from '@radix-ui/react-slot';

const textVariants = cva(
  cn(
    'text-foreground font-poppins text-base text-center',
    Platform.select({
      web: 'select-text',
    })
  ),
  {
    variants: {
      variant: {
        default: 'text-foreground font-lexend',

        /* ---------- Headings ---------- */
        h1: cn(
          'text-center text-4xl font-extrabold tracking-tight text-primary-foreground',
          Platform.select({ web: 'scroll-m-20 text-balance' })
        ),
        h2: cn(
          'text-3xl font-semibold tracking-tight text-primary',
          Platform.select({ web: 'scroll-m-20 first:mt-0' })
        ),
        h3: cn(
          'text-2xl font-semibold tracking-tight text-foreground',
          Platform.select({ web: 'scroll-m-20' })
        ),
        h4: cn(
          'text-xl font-semibold tracking-tight text-foreground',
          Platform.select({ web: 'scroll-m-20' })
        ),

        /* ---------- Text Content ---------- */
        p: 'mt-3 leading-7 sm:mt-6 text-foreground',
        lead: 'text-muted-foreground text-lg font-medium leading-7',
        large: 'text-lg font-semibold text-foreground',
        small: 'text-sm font-medium leading-none text-muted-foreground',
        muted: 'text-sm text-muted-foreground',

        /* ---------- Emphasis ---------- */
        blockquote:
          'mt-4 border-l-2 border-border pl-3 italic text-muted-foreground sm:mt-6 sm:pl-6',
        code: cn(
          'bg-muted relative rounded px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold text-accent-foreground'
        ),
        error: 'text-destructive font-lexend self-start text-xs',
        success: 'text-primary font-lexend self-start text-xs',
        info: 'text-secondary font-lexend self-start text-xs',

        /* ---------- Button & Utility ---------- */
        buttonText: 'text-primary-foreground font-alansans font-semibold tracking-wide',
        link: 'text-accent underline underline-offset-2 font-medium hover:text-accent-foreground',
      },
      weight: {
        light: 'font-light',
        normal: 'font-normal',
        medium: 'font-medium',
        semibold: 'font-semibold',
        bold: 'font-bold',
        extrabold: 'font-extrabold',
      },
      align: {
        left: 'text-left',
        center: 'text-center',
        right: 'text-right',
        justify: 'text-justify',
      },
    },
    compoundVariants: [
      {
        variant: 'blockquote',
        weight: 'light',
        class: 'italic opacity-90',
      },
    ],
    defaultVariants: {
      variant: 'default',
      weight: 'normal',
      align: 'left',
    },
  }
);

type TextVariantProps = VariantProps<typeof textVariants>;
type TextVariant = NonNullable<TextVariantProps['variant']>;

// ✅ use 'heading' instead of 'header'
const ROLE: Partial<Record<TextVariant, Role>> = {
  h1: 'heading',
  h2: 'heading',
  h3: 'heading',
  h4: 'heading',
  blockquote: Platform.select({ web: 'blockquote' as Role }),
  code: Platform.select({ web: 'code' as Role }),
};

const ARIA_LEVEL: Partial<Record<TextVariant, string>> = {
  h1: '1',
  h2: '2',
  h3: '3',
  h4: '4',
};

const TextClassContext = React.createContext<string | undefined>(undefined);

const TextSlot = Slot as unknown as React.ComponentType<React.ComponentProps<typeof RNText>>;

function CustomText({
  className,
  asChild = false,
  variant,
  weight,
  align,
  ...props
}: React.ComponentProps<typeof RNText> &
  TextVariantProps & {
    asChild?: boolean;
  }) {
  const textClass = React.useContext(TextClassContext);
  const Comp = asChild ? TextSlot : RNText;

  return (
    <Comp
      className={cn(textVariants({ variant, weight, align }), textClass, className)}
      role={variant ? ROLE[variant] : undefined}
      aria-level={variant ? ARIA_LEVEL[variant] : undefined}
      {...props}
    />
  );
}

export { CustomText, TextClassContext };
