import { ActivityIndicator, PressableProps } from 'react-native';
import CustomButton from '../ui/button';
import { cn } from '@/lib/utils';
import { CustomText } from '../ui/text';

const NextButton = ({
  className,
  textClassName,
  buttonTitle = 'Next',
  disabled,
  ...props
}: PressableProps & {
  className?: string;
  textClassName?: string;
  buttonTitle?: string;
}) => {
  const isLoading = disabled && buttonTitle.includes('Loading');

  return (
    <CustomButton
      className={cn(
        'mb-5 mt-auto flex-row items-center justify-center gap-2 rounded-full bg-primary',
        className
      )}
      disabled={disabled}
      onPress={props.onPress}>
      {isLoading && <ActivityIndicator size="small" color="white" />}
      <CustomText className={cn('font-alansans text-xl text-primary-foreground', textClassName)}>
        {buttonTitle}
      </CustomText>
    </CustomButton>
  );
};

export default NextButton;
