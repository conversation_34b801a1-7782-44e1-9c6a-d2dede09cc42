import NextButton from '@/components/onboarding/next-button';
import OnboardingBars from '@/components/onboarding/onboarding-bars';
import Form from '@/components/ui/form';
import CustomInput from '@/components/ui/input';
import SafeAreaContainer from '@/components/ui/safe-area-container';
import { CustomText } from '@/components/ui/text';
import Ionicons from '@expo/vector-icons/Ionicons';
import { zodResolver } from '@hookform/resolvers/zod';
import { router, useLocalSearchParams } from 'expo-router';
import { Controller, useForm } from 'react-hook-form';
import { View } from 'react-native';
import * as z from 'zod';

const formSchema = z.object({
  firstName: z.string().min(1, 'First name is required.'),
  lastName: z.string().min(1, 'Last name is required.'),
  email: z.email(),
});

type FormSchema = z.infer<typeof formSchema>;

const OnBoarding = () => {
  const { data } = useLocalSearchParams<{ data: string }>();
  const user = JSON.parse(data);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: user?.first_name,
      lastName: user?.last_name,
      email: user?.email,
    },
  });

  const onSubmit = (data: FormSchema) => {
    console.log(data);
    router.push('/onboarding-2');
  };

  return (
    <SafeAreaContainer className="flex flex-1 items-center justify-start pt-0">
      <Form>
        <View className="max-h-screen-safe flex-1">
          <OnboardingBars barsFilled={1} />
          <CustomText className="ios:text-5xl ios:pt-3 self-start font-poppins-bold text-3xl text-foreground">
            Let&apos;s get started
          </CustomText>
          <CustomText className="self-start font-lexend text-xl text-black">
            Tell us a bit about yourself
          </CustomText>
          <View className="my-3 flex w-full flex-col items-center justify-center gap-2">
            <CustomText className="self-start font-alansans-medium">First Name</CustomText>
            <View className="flex flex-row items-center justify-center rounded-xl bg-white pr-5">
              <Controller
                control={control}
                render={({ field: { onChange, onBlur, value } }) => (
                  <CustomInput
                    inputType="primary"
                    textContentType="givenName"
                    editable={false}
                    placeholder="Enter First Name"
                    onBlur={onBlur}
                    className="text-zinc-400"
                    onChangeText={onChange}
                    value={value}
                  />
                )}
                name="firstName"
              />
              <Ionicons name="lock-closed-outline" />
            </View>
            {errors.firstName && (
              <CustomText variant={'error'}>{errors.firstName.message}</CustomText>
            )}
            <CustomText className="self-start font-alansans-medium">Last Name</CustomText>
            <View className="flex flex-row items-center justify-center rounded-xl bg-white pr-5">
              <Controller
                control={control}
                render={({ field: { onChange, onBlur, value } }) => (
                  <CustomInput
                    inputType="primary"
                    textContentType="familyName"
                    placeholder="Enter Last Name"
                    onBlur={onBlur}
                    editable={false}
                    className="text-zinc-400"
                    onChangeText={onChange}
                    value={value}
                  />
                )}
                name="lastName"
              />
              <Ionicons name="lock-closed-outline" />
            </View>

            {errors.lastName && (
              <CustomText variant={'error'}>{errors.lastName.message}</CustomText>
            )}
            {/* <CustomText className="self-start font-alansans-medium">
              Phone Number (optional)
            </CustomText>
            <Controller
              control={control}
              render={({ field: { onChange, onBlur, value } }) => (
                <CustomInput
                  inputType="primary"
                  textContentType="telephoneNumber"
                  placeholder="Enter Phone Number (optional)"
                  onBlur={onBlur}
                  onChangeText={(text) => onChange(text.replace(/\s/g, ''))}
                  value={value}
                />
              )}
              name="phoneNumber"
            />
            {errors.phoneNumber && (
              <CustomText variant={'error'}>{errors.phoneNumber.message}</CustomText>
            )} */}
            <CustomText className="self-start font-alansans-medium">Email</CustomText>
            <View className="flex flex-row items-center justify-center rounded-xl bg-white pr-5">
              <Controller
                control={control}
                render={({ field: { value } }) => (
                  <CustomInput
                    inputType="primary"
                    editable={false}
                    textContentType="emailAddress"
                    value={value}
                    className="text-zinc-400"
                  />
                )}
                name="email"
              />
              <Ionicons name="lock-closed-outline" />
            </View>
          </View>
          <NextButton className="mt-auto rounded-full" onPress={handleSubmit(onSubmit)}>
            <CustomText className="font-alansans text-primary-foreground">Next</CustomText>
          </NextButton>
        </View>
      </Form>
    </SafeAreaContainer>
  );
};

export default OnBoarding;
