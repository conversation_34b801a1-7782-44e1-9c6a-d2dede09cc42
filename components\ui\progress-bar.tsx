import { cn } from '@/lib/utils';
import { View } from 'react-native';

interface ProgressBarProps {
  progress: number;
  isBeyond?: boolean;
}

const ProgressBar = ({ progress, isBeyond }: ProgressBarProps) => {
  return (
    <View className="h-3 w-full rounded-full bg-secondary">
      <View
        className={cn('h-3 rounded-full', isBeyond ? 'bg-destructive' : ' bg-primary')}
        style={{ width: `${progress * 100}%` }}
      />
    </View>
  );
};

export default ProgressBar;
