import { useFonts as usePoppinsFonts } from '@expo-google-fonts/poppins/useFonts';
import { Poppins_100Thin } from '@expo-google-fonts/poppins/100Thin';
import { Poppins_100Thin_Italic } from '@expo-google-fonts/poppins/100Thin_Italic';
import { Poppins_200ExtraLight } from '@expo-google-fonts/poppins/200ExtraLight';
import { Poppins_200ExtraLight_Italic } from '@expo-google-fonts/poppins/200ExtraLight_Italic';
import { Poppins_300Light } from '@expo-google-fonts/poppins/300Light';
import { Poppins_300Light_Italic } from '@expo-google-fonts/poppins/300Light_Italic';
import { Poppins_400Regular } from '@expo-google-fonts/poppins/400Regular';
import { Poppins_400Regular_Italic } from '@expo-google-fonts/poppins/400Regular_Italic';
import { Poppins_500Medium } from '@expo-google-fonts/poppins/500Medium';
import { Poppins_500Medium_Italic } from '@expo-google-fonts/poppins/500Medium_Italic';
import { Poppins_600SemiBold } from '@expo-google-fonts/poppins/600SemiBold';
import { Poppins_600SemiBold_Italic } from '@expo-google-fonts/poppins/600SemiBold_Italic';
import { Poppins_700Bold } from '@expo-google-fonts/poppins/700Bold';
import { Poppins_700Bold_Italic } from '@expo-google-fonts/poppins/700Bold_Italic';
import { Poppins_800ExtraBold } from '@expo-google-fonts/poppins/800ExtraBold';
import { Poppins_800ExtraBold_Italic } from '@expo-google-fonts/poppins/800ExtraBold_Italic';
import { Poppins_900Black } from '@expo-google-fonts/poppins/900Black';
import { Poppins_900Black_Italic } from '@expo-google-fonts/poppins/900Black_Italic';

import { useFonts as useLexendDecaFonts } from '@expo-google-fonts/lexend-deca/useFonts';
import { LexendDeca_100Thin } from '@expo-google-fonts/lexend-deca/100Thin';
import { LexendDeca_200ExtraLight } from '@expo-google-fonts/lexend-deca/200ExtraLight';
import { LexendDeca_300Light } from '@expo-google-fonts/lexend-deca/300Light';
import { LexendDeca_400Regular } from '@expo-google-fonts/lexend-deca/400Regular';
import { LexendDeca_500Medium } from '@expo-google-fonts/lexend-deca/500Medium';
import { LexendDeca_600SemiBold } from '@expo-google-fonts/lexend-deca/600SemiBold';
import { LexendDeca_700Bold } from '@expo-google-fonts/lexend-deca/700Bold';
import { LexendDeca_800ExtraBold } from '@expo-google-fonts/lexend-deca/800ExtraBold';
import { LexendDeca_900Black } from '@expo-google-fonts/lexend-deca/900Black';

import { useFonts as useAlanSansFonts } from '@expo-google-fonts/alan-sans/useFonts';
import { AlanSans_300Light } from '@expo-google-fonts/alan-sans/300Light';
import { AlanSans_400Regular } from '@expo-google-fonts/alan-sans/400Regular';
import { AlanSans_500Medium } from '@expo-google-fonts/alan-sans/500Medium';
import { AlanSans_600SemiBold } from '@expo-google-fonts/alan-sans/600SemiBold';
import { AlanSans_700Bold } from '@expo-google-fonts/alan-sans/700Bold';
import { AlanSans_800ExtraBold } from '@expo-google-fonts/alan-sans/800ExtraBold';
import { AlanSans_900Black } from '@expo-google-fonts/alan-sans/900Black';

import * as SplashScreen from 'expo-splash-screen';

SplashScreen.preventAutoHideAsync();

const useMyFonts = () => {
  const [poppinsFontsLoaded, poppinsError] = usePoppinsFonts({
    Poppins_100Thin,
    Poppins_100Thin_Italic,
    Poppins_200ExtraLight,
    Poppins_200ExtraLight_Italic,
    Poppins_300Light,
    Poppins_300Light_Italic,
    Poppins_400Regular,
    Poppins_400Regular_Italic,
    Poppins_500Medium,
    Poppins_500Medium_Italic,
    Poppins_600SemiBold,
    Poppins_600SemiBold_Italic,
    Poppins_700Bold,
    Poppins_700Bold_Italic,
    Poppins_800ExtraBold,
    Poppins_800ExtraBold_Italic,
    Poppins_900Black,
    Poppins_900Black_Italic,
  });

  const [lexendDecaFontsLoaded, lexendDecaError] = useLexendDecaFonts({
    LexendDeca_100Thin,
    LexendDeca_200ExtraLight,
    LexendDeca_300Light,
    LexendDeca_400Regular,
    LexendDeca_500Medium,
    LexendDeca_600SemiBold,
    LexendDeca_700Bold,
    LexendDeca_800ExtraBold,
    LexendDeca_900Black,
  });

  const [alanSansFontsLoaded, alanSansError] = useAlanSansFonts({
    AlanSans_300Light,
    AlanSans_400Regular,
    AlanSans_500Medium,
    AlanSans_600SemiBold,
    AlanSans_700Bold,
    AlanSans_800ExtraBold,
    AlanSans_900Black,
  });

  return {
    lexendDecaFontsLoaded,
    poppinsFontsLoaded,
    poppinsError,
    lexendDecaError,
    alanSansFontsLoaded,
    alanSansError,
  };
};

export default useMyFonts;
