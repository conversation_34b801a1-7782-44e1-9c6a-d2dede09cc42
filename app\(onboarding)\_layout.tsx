import { Stack } from 'expo-router';

const OnboardingLayout = () => {
  return (
    <Stack
      screenOptions={{
        headerShown: true,
        headerBackVisible: true,
      }}>
      <Stack.Screen
        name="onboarding-1"
        options={{
          title: 'Personal Details',
          headerBackTitle: 'Back',
          headerTitleStyle: {
            fontFamily: 'Poppins_400Regular',
          },
          headerBackTitleStyle: {
            fontFamily: 'LexendDeca_400Regular',
          },
        }}
      />
      <Stack.Screen
        name="onboarding-2"
        options={{
          title: 'Your Physique',
          headerBackTitle: 'Back',
          headerTitleStyle: {
            fontFamily: 'Poppins_400Regular',
          },
          headerBackTitleStyle: {
            fontFamily: 'LexendDeca_400Regular',
          },
        }}
      />
      <Stack.Screen
        name="onboarding-3"
        options={{
          title: 'Dietary Restrictions',
          headerBackTitle: 'Back',
          headerTitleStyle: {
            fontFamily: 'Poppins_400Regular',
          },
          headerBackTitleStyle: {
            fontFamily: 'LexendDeca_400Regular',
          },
        }}
      />
      <Stack.Screen
        name="onboarding-4"
        options={{
          title: 'Onboarding Complete',
          headerBackTitle: 'Back',
          headerTitleStyle: {
            fontFamily: 'Poppins_400Regular',
          },
          headerBackTitleStyle: {
            fontFamily: 'LexendDeca_400Regular',
          },
        }}
      />
    </Stack>
  );
};

export default OnboardingLayout;
