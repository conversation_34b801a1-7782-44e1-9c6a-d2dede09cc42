import { View, Image } from 'react-native';
import { CustomText } from '../ui/text';

const PetCard = () => {
  return (
    <View className="p-1 mt-5 flex h-32 w-full flex-row items-center justify-start rounded-3xl bg-muted">
      <Image
        source={require('@/assets/healthy-cat.png')}
        className="ios:translate-x-0 h-full w-fit -translate-x-8"
        resizeMode="contain"
      />
      <View className="ios:translate-x-0 w-full -translate-x-14">
        <CustomText>Mewto is happy!</CustomText>
        <CustomText className="text-accent">You have been eating well!</CustomText>
      </View>
    </View>
  );
};

export default PetCard;
