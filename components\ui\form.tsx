import { ReactNode } from 'react';
import { Keyboard, KeyboardAvoidingView, ScrollView, TouchableWithoutFeedback } from 'react-native';

const Form = ({ children }: { children: ReactNode }) => {
  return (
    <KeyboardAvoidingView behavior={'padding'} className="flex-1">
      <ScrollView
        className="bg-background"
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flexGrow: 1 }}>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>{children}</TouchableWithoutFeedback>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default Form;
