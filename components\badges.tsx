import { cn } from '@/lib/utils';
import { JSX } from 'react';
import { View } from 'react-native';
import { CustomText } from './ui/text';

export interface BadgeProps {
  name: string;
  icon: ({
    color,
    className,
    size,
  }: {
    color: string;
    size: number;
    className?: string;
  }) => JSX.Element;
  isUnlocked: boolean;
}

export interface BadgeDetailedProps extends BadgeProps {
  description: string;
}

export const BadgeShort = (item: BadgeProps) => {
  return (
    <View className="flex max-w-[100px] flex-col items-center justify-center gap-3">
      <item.icon size={25} color="red" className={cn(item.isUnlocked ? '' : 'opacity-30')} />
      <CustomText className={cn('text-foreground', item.isUnlocked ? '' : 'opacity-30')}>
        {item.name}
      </CustomText>
    </View>
  );
};

export const BadgeDetailed = (item: BadgeDetailedProps) => {
  return (
    <View className="flex w-full flex-row items-center justify-center gap-3 rounded-xl bg-card p-3">
      <item.icon
        size={35}
        color="red"
        className={cn(item.isUnlocked ? 'bg-muted' : 'bg-gray-300 opacity-30')}
      />

      <View className="flex w-full flex-1 flex-col items-center justify-center gap-2">
        <CustomText
          className={cn(
            'w-full font-poppins-semibold text-xl text-foreground',
            item.isUnlocked ? '' : 'opacity-60'
          )}>
          {item.name}
        </CustomText>
        <CustomText
          className={cn('w-full font-lexend text-foreground', item.isUnlocked ? '' : 'opacity-60')}>
          {item.description}
        </CustomText>
      </View>
    </View>
  );
};
