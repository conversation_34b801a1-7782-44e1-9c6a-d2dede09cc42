// Usage:
// primary for direct input without suffix or anything, if need
// suffix, use inputType:'secondary' and children should be the suffix

import { cn } from '@/lib/utils';
import { TextInput, TextInputProps, View } from 'react-native';

const inputVariant = {
  primary:
    'h-12 w-full rounded-xl bg-white px-5 font-lexend placeholder:font-lexend text-black placeholder:text-gray-400',
  secondary:
    'h-12 w-full rounded-xl bg-white pr-5 font-lexend placeholder:font-lexend text-black placeholder:text-gray-400 flex flex-row items-center',
};

interface CustomInputProps extends TextInputProps {
  inputType: keyof typeof inputVariant;
}

const CustomInput = (props: CustomInputProps) => {
  const { inputType, children, className, ...otherProps } = props;

  if (inputType === 'primary') {
    return <TextInput className={cn(inputVariant.primary, className)} {...otherProps} />;
  }

  return (
    <View className={cn(inputVariant.secondary, className)}>
      <TextInput
        className={cn(
          inputVariant.primary,
          'h-full flex-1 pl-5 pr-0 text-gray-900 placeholder-gray-400'
        )}
        {...otherProps}
      />
      {children}
    </View>
  );
};

export default CustomInput;
