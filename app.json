{"expo": {"name": "Eatable?", "slug": "eatable?", "version": "1.0.0", "web": {"favicon": "./assets/icon.png", "bundler": "metro"}, "experiments": {"tsconfigPaths": true, "reactCompiler": true}, "plugins": ["expo-router", "expo-font", "@react-native-google-signin/google-signin", ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera", "microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone", "recordAudioAndroid": false}], "expo-secure-store"], "orientation": "default", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.beyondbinary.eatable"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/icon.png", "backgroundColor": "#ffffff"}, "package": "com.beyondbinary.eatable", "permissions": ["android.permission.CAMERA"]}, "scheme": "eatable"}}