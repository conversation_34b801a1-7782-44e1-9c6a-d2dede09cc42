import CustomButton from '@/components/ui/button';
import { Modal, View } from 'react-native';
import { CustomText } from '../ui/text';

const ImageCapturedPopup = ({
  visible,
  onProceed,
  onRetake,
}: {
  visible: boolean;
  onProceed: () => void;
  onRetake: () => void;
}) => (
  <Modal visible={visible} animationType="fade" transparent onRequestClose={onRetake}>
    <View className="flex-1 items-center justify-center bg-black/40">
      <View className="w-80 items-center rounded-xl bg-[#F7FBFB] p-6">
        <CustomText className="mb-3 font-poppins-bold text-2xl text-neutral-900">
          Image Captured
        </CustomText>
        <CustomText className="mb-6 text-center text-base text-neutral-700">
          Do you want to retake the image or proceed to scanning?
        </CustomText>
        <CustomButton buttonVariant="primary" onPress={onProceed} className="mb-3">
          <CustomText variant={'buttonText'} align={'center'}>
            Proceed to Scan
          </CustomText>
        </CustomButton>
        <CustomButton buttonVariant="secondary" onPress={onRetake} className="bg-muted">
          <CustomText>Retake</CustomText>
        </CustomButton>
      </View>
    </View>
  </Modal>
);

export default ImageCapturedPopup;
