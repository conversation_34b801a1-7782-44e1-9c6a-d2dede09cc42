import CustomButton from '@/components/ui/button';
import ProgressBar from '@/components/ui/progress-bar';
import SafeAreaContainer from '@/components/ui/safe-area-container';
import { Option, Select } from '@/components/ui/select';
import { CustomText } from '@/components/ui/text';
import { quantityMetrics, quantityValues } from '@/constants/constant-scan-results';
import { COMMON_TOAST_PROPS } from '@/constants/toast-common';
import { useAddToLog } from '@/services/log/add-to-log';
import { useScanData } from '@/stores/scanStore';
import { Redirect, router } from 'expo-router';
import { useState } from 'react';
import { ActivityIndicator, Image, ScrollView, View } from 'react-native';
import { showToast } from 'react-native-nitro-toast';

type quantityMetric = 'g' | 'mg';

const AddToLog = () => {
  const [quantity, setQuantity] = useState(100);
  const [metric, setMetric] = useState<quantityMetric>('g');

  const { scanData, clearScanData } = useScanData();
  const { mutate, isPending } = useAddToLog({
    onError(error) {
      console.error('Error adding to log:', error);
      showToast('Failed to add to log!', {
        ...COMMON_TOAST_PROPS,
        type: 'error',
      });
    },
  });

  // Redirect if no scan data available
  if (!scanData) {
    showToast('No scan data available. Please scan again!', {
      type: 'warning',
      title: 'Missing Data',
      ...COMMON_TOAST_PROPS,
    });
    return <Redirect href={'/scan-and-check'} />;
  }

  // Handle both scan results and saved meal data formats
  const macros = scanData.macros || [];
  const food_name = scanData.food_name || 'Unknown Food';
  const calories = scanData.calories || '0 kcal';

  const handleAddToLog = () => {
    const data = {
      nutrition_label: scanData.id,
      consumed: true,
      quantity,
      metric,
    };
    console.log(data);
    mutate(data);
    showToast('Meal Logged Successfully!', {
      ...COMMON_TOAST_PROPS,
      type: 'success',
    });
    clearScanData();
    router.replace('/scan-and-check');
  };

  return (
    <SafeAreaContainer className="ios:-mt-10 flex-1 pt-0">
      <ScrollView>
        <View className="flex flex-col items-center justify-between rounded-xl bg-card px-6 pt-6">
          <View className="mb-10 flex w-full flex-row items-center justify-start gap-6">
            <Image
              source={{
                uri: scanData.imageURI || '',
              }}
              className="h-20 w-20 rounded-xl"
              resizeMode="cover"
            />
            <View className="flex-1">
              <CustomText variant={'h3'} className="flex-wrap font-poppins text-primary">
                {food_name}
              </CustomText>
              <CustomText variant={'info'} className="font-poppins text-accent">
                {calories} Calories per 100g
              </CustomText>
            </View>
          </View>
          <View className="flex w-full flex-row items-center justify-between">
            <View className="flex w-full flex-1 flex-col gap-3">
              <CustomText variant={'info'} className="ml-5 self-start text-lg text-accent">
                Quantity
              </CustomText>
              <Select
                selectedValue={quantity}
                onValueChange={(itemValue) => setQuantity(itemValue as number)}>
                {quantityValues.map((item) => (
                  <Option {...item} key={item.value} />
                ))}
              </Select>
            </View>
            <View className="flex w-full flex-1 flex-col gap-3">
              <CustomText variant={'info'} className="ml-5 self-start text-lg text-accent">
                Metric
              </CustomText>
              <Select
                selectedValue={metric}
                onValueChange={(itemValue) => setMetric(itemValue as quantityMetric)}>
                {quantityMetrics.map((item) => (
                  <Option {...item} key={item.value} />
                ))}
              </Select>
            </View>
          </View>
        </View>

        <View className="mt-3 rounded-xl bg-card p-6">
          <CustomText variant="h3" className="mb-5">
            Macro Breakdown
          </CustomText>
          <View className="gap-y-3">
            {macros.map((macro) => (
              <View key={macro.id} className="w-full flex-1">
                <View className="flex flex-row items-center justify-between">
                  <CustomText>{macro.name}</CustomText>
                  <CustomText>{macro.value} / 100g</CustomText>
                </View>
                <ProgressBar progress={Number(macro.value) / 100} />
              </View>
            ))}
          </View>
        </View>
      </ScrollView>
      <CustomButton
        onPress={handleAddToLog}
        buttonVariant="primary"
        disabled={isPending}
        className="mt-auto flex-row items-center gap-2 rounded-full">
        {isPending && <ActivityIndicator size="small" color="white" />}
        <CustomText variant={'buttonText'}>
          {isPending ? 'Logging...' : 'Log Food Entry'}
        </CustomText>
      </CustomButton>
    </SafeAreaContainer>
  );
};

export default AddToLog;
