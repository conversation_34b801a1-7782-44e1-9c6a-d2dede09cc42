import axios from 'axios';
import { SecureStorage } from './utils';
import { showToast } from 'react-native-nitro-toast';
import { COMMON_TOAST_PROPS } from '@/constants/toast-common';
import { router } from 'expo-router';

const apiClient = axios.create({
  // baseURL: 'https://eatable-service.onrender.com',
  baseURL: 'https://5cefcafc9da4.ngrok-free.app',
});

apiClient.interceptors.request.use(
  async (config) => {
    console.log('➡️ Sending request to:', config.baseURL, config.url);
    console.log('Headers:', config.headers);
    try {
      const token = await SecureStorage.getAccessToken();
      console.log('token in axios', token);
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.error('Failed to load token for request', error);
      showToast(`Failed to load token for request, ${error}`, {
        type: 'error',
        ...COMMON_TOAST_PROPS,
      });
    }
    return config;
  },
  (error) => {
    console.error('Error in axios 31: ', error);
    return Promise.reject(error);
  }
);

const refreshToken = async () => {
  try {
    const refreshToken = await SecureStorage.getRefreshToken();
    if (refreshToken) {
      const response = await axios.post(
        'https://eatable-service.onrender.com/api/v1/auth/token/refresh/',
        {
          refresh: refreshToken,
        }
      );
      const { access, refresh } = response.data;
      await SecureStorage.setAccessToken(access);
      await SecureStorage.setRefreshToken(refresh);
      return access;
    }
  } catch (error) {
    console.error('Failed to refresh token', error);
    showToast('Session expired. Please login again.', {
      type: 'error',
      ...COMMON_TOAST_PROPS,
    });
    await SecureStorage.clearAll();
    router.replace('/auth');
    return null;
  }
};

apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    if (error.response && error.response.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      const newAccessToken = await refreshToken();
      if (newAccessToken) {
        originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;
        return apiClient(originalRequest);
      }
    }
    if (error.response) {
      if (error.response.status === 401) {
        console.error('Unauthorized refresh token: logging out', error.response.data);
        await SecureStorage.clearAll();
        router.replace('/auth');
      }
    } else if (error.request) {
      console.error('Network Error:', error.message);
      showToast('Unable to reach the server. Please check your internet connection.', {
        type: 'error',
        ...COMMON_TOAST_PROPS,
      });
    } else {
      console.error('Axios config error:', error.message);
    }
    return Promise.reject(error);
  }
);

export default apiClient;
