import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import * as SecureStore from 'expo-secure-store';
import { GoogleAuthResponse } from '@/services/auth/types';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

const SECURE_OPTIONS: SecureStore.SecureStoreOptions = {
  keychainAccessible: SecureStore.AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY,
};

export enum SecureKeys {
  ACCESS_TOKEN = 'token',
  REFRESH_TOKEN = 'refresh_token',
  USER_PROFILE = 'user_profile',
}

async function setSecureItem<T = string>(key: SecureKeys, value: T): Promise<void> {
  try {
    const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
    await SecureStore.setItemAsync(key, serializedValue, SECURE_OPTIONS);
  } catch (err) {
    console.error(`[SecureStore] Failed to save key "${key}"`, err);
  }
}

export async function getSecureItem<T = string>(key: SecureKeys): Promise<T | null> {
  try {
    const result = await SecureStore.getItemAsync(key);
    if (!result) return null;

    try {
      return JSON.parse(result) as T;
    } catch {
      // Return as string if not JSON
      return result as unknown as T;
    }
  } catch (err) {
    console.error(`[SecureStore] Failed to get key "${key}"`, err);
    return null;
  }
}

async function deleteSecureItem(key: SecureKeys): Promise<void> {
  try {
    await SecureStore.deleteItemAsync(key);
  } catch (err) {
    console.error(`[SecureStore] Failed to delete key "${key}"`, err);
  }
}

export const SecureStorage = {
  setAccessToken: async (token: string) => await setSecureItem(SecureKeys.ACCESS_TOKEN, token),

  getAccessToken: async () => await getSecureItem<string>(SecureKeys.ACCESS_TOKEN),

  removeAccessToken: async () => await deleteSecureItem(SecureKeys.ACCESS_TOKEN),

  setRefreshToken: async (token: string) => await setSecureItem(SecureKeys.REFRESH_TOKEN, token),

  getRefreshToken: async () => await getSecureItem<string>(SecureKeys.REFRESH_TOKEN),

  removeRefreshToken: async () => await deleteSecureItem(SecureKeys.REFRESH_TOKEN),

  setUserProfile: async (profile: Record<string, any>) =>
    await setSecureItem(SecureKeys.USER_PROFILE, profile),

  getUserProfile: async () =>
    await getSecureItem<GoogleAuthResponse['user']>(SecureKeys.USER_PROFILE),

  clearAll: async () => {
    await Promise.all(Object.values(SecureKeys).map((key) => SecureStore.deleteItemAsync(key)));
  },
};
