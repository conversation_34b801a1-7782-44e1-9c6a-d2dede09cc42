import { cn } from '@/lib/utils';
import { View } from 'react-native';

const OnboardingBars = ({
  barsFilled,
  className,
}: {
  barsFilled: 1 | 2 | 3 | 4;
  className?: string;
}) => {
  const filledColor = 'bg-primary';
  const unfilledColor = 'bg-secondary opacity-35';
  const barClass = 'flex h-2 w-full flex-1 rounded-lg';

  return (
    <View className={cn('mb-8 flex flex-row items-center justify-center gap-2', className)}>
      <View className={cn(barClass, filledColor)} />
      <View className={cn(barClass, barsFilled >= 2 ? filledColor : unfilledColor)} />
      <View className={cn(barClass, barsFilled >= 3 ? filledColor : unfilledColor)} />
      <View className={cn(barClass, barsFilled >= 4 ? filledColor : unfilledColor)} />
    </View>
  );
};

export default OnboardingBars;
