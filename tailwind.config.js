const { hairlineWidth } = require('nativewind/theme');

/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: 'class',
  content: ['./app/**/*.{ts,tsx}', './components/**/*.{ts,tsx}'],
  presets: [require('nativewind/preset')],
  theme: {
    extend: {
      fontFamily: {
        poppins: 'var(--font-poppins)',
        'poppins-thin': 'var(--font-poppins-thin)',
        'poppins-thin-italic': 'var(--font-poppins-thin-italic)',
        'poppins-extralight': 'var(--font-poppins-extralight)',
        'poppins-extralight-italic': 'var(--font-poppins-extralight-italic)',
        'poppins-light': 'var(--font-poppins-light)',
        'poppins-light-italic': 'var(--font-poppins-light-italic)',
        'poppins-regular': 'var(--font-poppins-regular)',
        'poppins-italic': 'var(--font-poppins-italic)',
        'poppins-medium': 'var(--font-poppins-medium)',
        'poppins-medium-italic': 'var(--font-poppins-medium-italic)',
        'poppins-semibold': 'var(--font-poppins-semibold)',
        'poppins-semibold-italic': 'var(--font-poppins-semibold-italic)',
        'poppins-bold': 'var(--font-poppins-bold)',
        'poppins-bold-italic': 'var(--font-poppins-bold-italic)',
        'poppins-extrabold': 'var(--font-poppins-extrabold)',
        'poppins-extrabold-italic': 'var(--font-poppins-extrabold-italic)',
        'poppins-black': 'var(--font-poppins-black)',
        'poppins-black-italic': 'var(--font-poppins-black-italic)',

        lexend: 'var(--font-lexend)',

        'lexend-thin': 'var(--font-lexend-thin)',
        'lexend-extralight': 'var(--font-lexend-extralight)',
        'lexend-light': 'var(--font-lexend-light)',
        'lexend-regular': 'var(--font-lexend-regular)',
        'lexend-medium': 'var(--font-lexend-medium)',
        'lexend-semibold': 'var(--font-lexend-semibold)',
        'lexend-bold': 'var(--font-lexend-bold)',
        'lexend-extrabold': 'var(--font-lexend-extrabold)',
        'lexend-black': 'var(--font-lexend-black)',

        alansans: 'var(--font-alansans)',

        'alansans-light': 'var(--font-alansans-light)',
        'alansans-regular': 'var(--font-alansans-regular)',
        'alansans-medium': 'var(--font-alansans-medium)',
        'alansans-semibold': 'var(--font-alansans-semibold)',
        'alansans-bold': 'var(--font-alansans-bold)',
        'alansans-extrabold': 'var(--font-alansans-extrabold)',
        'alansans-black': 'var(--font-alansans-black)',
      },
      colors: {
        border: 'var(--border)',
        input: 'var(--input)',
        ring: 'var(--ring)',
        background: 'var(--background)',
        foreground: 'var(--foreground)',
        primary: {
          DEFAULT: 'var(--primary)',
          foreground: 'var(--primary-foreground)',
        },
        secondary: {
          DEFAULT: 'var(--secondary)',
          foreground: 'var(--secondary-foreground)',
        },
        tertiary: {
          DEFAULT: 'var(--tertiary)',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'var(--muted)',
          foreground: 'var(--muted-foreground)',
        },
        accent: {
          DEFAULT: 'var(--accent)',
          foreground: 'var(--accent-foreground)',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'var(--card)',
          foreground: 'var(--card-foreground)',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      borderWidth: {
        hairline: hairlineWidth(),
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
    },
  },
  future: {
    hoverOnlyWhenSupported: true,
  },
  plugins: [require('tailwindcss-animate')],
};
