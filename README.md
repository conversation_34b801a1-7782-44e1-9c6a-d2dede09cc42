# Setup and start:

1. Install node and npm
2. `pnpm i; pnpm start`
3. Open emulator or expo go app in physical device

# Colors

foreground - for text
muted - disabled
accent - random
destructive - for deletions or logout etc

ex:

- primary - for bg
- primary-foreground - for text on primary bg

# Fonts:

- buttons:font-alansans
- placeholder/labels:font-lexend
- headings & normal text:font-poppins
