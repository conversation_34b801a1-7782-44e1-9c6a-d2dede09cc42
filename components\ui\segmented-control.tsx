import { View, Pressable, StyleSheet } from 'react-native';
import { CustomText } from './text';

interface SegmentedControlProps {
  options: string[];
  value: string;
  onValueChange: (value: string) => void;
  className?: string;
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    gap: 8,
    borderRadius: 9999,
    backgroundColor: '#e5e7eb',
    padding: 4,
  },
  button: {
    flex: 1,
    borderRadius: 9999,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  buttonActive: {
    backgroundColor: '#ffffff',
  },
  buttonInactive: {
    backgroundColor: 'transparent',
  },
  textActive: {
    textAlign: 'center',
    fontSize: 14,
    fontWeight: '500',
    color: '#006d77',
  },
  textInactive: {
    textAlign: 'center',
    fontSize: 14,
    fontWeight: '500',
    color: '#9ca3af',
  },
});

export const SegmentedControl = ({ options, value, onValueChange }: SegmentedControlProps) => {
  return (
    <View style={styles.container}>
      {options.map((option) => (
        <Pressable
          key={option}
          onPress={() => onValueChange(option)}
          style={[styles.button, value === option ? styles.buttonActive : styles.buttonInactive]}>
          <CustomText style={value === option ? styles.textActive : styles.textInactive}>
            {option}
          </CustomText>
        </Pressable>
      ))}
    </View>
  );
};
