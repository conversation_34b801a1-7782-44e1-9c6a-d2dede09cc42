import { BadgeShort } from '@/components/badges';
import CustomButton from '@/components/ui/button';
import SafeAreaContainer from '@/components/ui/safe-area-container';
import { CustomText } from '@/components/ui/text';
import { badges } from '@/constants/badges';
import { activityLevel, goalOptions } from '@/constants/onboarding';
import { SecureStorage } from '@/lib/utils';
import { handleGoogleSignOut } from '@/services/auth/google-auth';
import { useGetMe } from '@/services/auth/me';
import { Ionicons } from '@expo/vector-icons';
import { Link, router, Stack } from 'expo-router';
import { ScrollView, View } from 'react-native';

const ProfileScreen = () => {
  const { data } = useGetMe({});
  const handleLogout = async () => {
    await handleGoogleSignOut();
    await SecureStorage.clearAll();
    router.replace('/auth');
  };

  const userInitial = data?.first_name?.[0]?.toUpperCase() ?? '?';

  let intolerancesText = '';

  try {
    if (data?.food_intolerances) {
      const parsed = JSON.parse(data.food_intolerances);

      if (Array.isArray(parsed) && parsed.length > 0) {
        intolerancesText = parsed.join(', ');
      } else if (typeof parsed === 'object' && Object.keys(parsed).length > 0) {
        intolerancesText = Object.values(parsed).join(', ');
      } else if (typeof parsed === 'string' && parsed.trim()) {
        intolerancesText = parsed;
      }
    }
  } catch {
    // If it's not JSON, just use as-is
    intolerancesText = data?.food_intolerances?.trim() || '';
  }

  return (
    <SafeAreaContainer className="flex-1 bg-background pt-0 ">
      <Stack.Screen
        options={{
          headerTitle: 'Profile',
          headerBackTitle: 'Back',
          headerTitleStyle: {
            fontFamily: 'Poppins_400Regular',
          },
          headerBackTitleStyle: {
            fontFamily: 'LexendDeca_400Regular',
          },
        }}
      />
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerClassName="w-full flex-grow items-center ">
        {/* Avatar */}
        <View className="my-4 h-32 w-32 items-center justify-center rounded-full bg-secondary shadow-md">
          {/* {data?.profile_picture ? (
            <Image
              source={{ uri: data.profile_picture }}
              className="h-full w-full rounded-full"
              resizeMode="cover"
            />
          ) : ( */}
          <CustomText className="font-poppins-medium text-6xl text-white">{userInitial}</CustomText>
          {/* )} */}
        </View>
        {/* Name and Email */}
        <CustomText className="mb-1 font-poppins-bold text-2xl text-gray-900">
          {data?.first_name} {data?.last_name}
        </CustomText>
        <CustomText className="mb-4 text-gray-500">{data?.email}</CustomText>
        {/* Info Card */}
        <CustomText variant={'h4'} className="mb-3 self-start">
          Personal Info
        </CustomText>
        <View className="w-full rounded-xl border border-gray-200 bg-card p-5 shadow-sm">
          {data?.age && (
            <CustomText className="mb-1 text-base text-gray-700">Age: {data.age}</CustomText>
          )}
          {data?.gender && (
            <CustomText className="mb-1 text-base text-gray-700">
              Gender: {data.gender.slice(0)[0].toLocaleUpperCase() + data.gender.slice(1)}
            </CustomText>
          )}
          {data?.height && (
            <CustomText className="mb-1 text-base text-gray-700">
              Height: {data.height} cm
            </CustomText>
          )}
          {data?.weight && (
            <CustomText className="mb-1 text-base text-gray-700">
              Weight: {data.weight} kg
            </CustomText>
          )}
          {data?.bmi && (
            <CustomText className="mb-1 text-base text-gray-700">BMI: {data.bmi}</CustomText>
          )}
          {data?.dietary_preference && (
            <CustomText className="mb-1 text-base text-gray-700">
              Dietary Preference:{' '}
              {data.dietary_preference
                .split('_')
                .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                .join(' ')}
            </CustomText>
          )}

          {intolerancesText ? (
            <CustomText className="mb-1 text-base text-gray-700">
              Food Intolerances: {intolerancesText}
            </CustomText>
          ) : null}

          {data?.goal && (
            <CustomText className="text-base text-gray-700">
              Goal: {goalOptions.find((option) => option.value === data.goal)?.label}
            </CustomText>
          )}
          {data?.activity_level && (
            <CustomText className="text-base text-gray-700">
              Activity Level:{' '}
              {activityLevel.find((option) => option.value === data.activity_level)?.label}
            </CustomText>
          )}
        </View>
        <View className="mb-5 mt-10 flex w-full flex-row items-center justify-between gap-4">
          <CustomText variant={'h4'} className="flex w-fit flex-wrap">
            Your Badges
          </CustomText>
          <Link href="/badges-and-acheivements" className="w-fit bg-transparent px-4">
            <CustomText className=" text-primary">View All</CustomText>
          </Link>
        </View>
        <View className="mb-5 w-full rounded-xl bg-card p-5">
          <ScrollView
            contentContainerClassName="flex flex-row gap-6 items-center justify-center mb-3"
            horizontal
            showsHorizontalScrollIndicator={false}>
            {badges.map((item, index) => (
              <BadgeShort key={index} {...item} />
            ))}
          </ScrollView>
        </View>
      </ScrollView>
      <CustomButton
        buttonVariant="destructive"
        onPress={handleLogout}
        className="my-3 flex w-full flex-row items-center justify-center gap-3 rounded-xl shadow-md">
        <Ionicons name="exit-outline" size={20} color={'white'} />
        <CustomText className="text-lg font-semibold text-destructive-foreground">
          Logout
        </CustomText>
      </CustomButton>
    </SafeAreaContainer>
  );
};

export default ProfileScreen;
