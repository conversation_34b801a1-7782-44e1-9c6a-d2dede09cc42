import { create } from 'zustand';
import { ScanResponse } from '@/services/scan/types';
import { Macro } from '@/components/scan-and-check/types';

interface ScanData extends Omit<ScanResponse, 'macros'> {
  macros: Macro[];
  imageURI?: string; // Local URI for display (backward compatibility)
  is_consumed?: boolean;
}

interface ScanStore {
  scanData: ScanData | null;
  setScanData: (data: ScanData) => void;
  clearScanData: () => void;

  // For meal history add functionality
  pendingMealData: ScanData | null;
  setPendingMealData: (data: ScanData) => void;
  clearPendingMealData: () => void;
}

export const useScanStore = create<ScanStore>((set) => ({
  scanData: null,
  setScanData: (data) => set({ scanData: data }),
  clearScanData: () => set({ scanData: null }),

  pendingMealData: null,
  setPendingMealData: (data) => set({ pendingMealData: data }),
  clearPendingMealData: () => set({ pendingMealData: null }),
}));

// Convenience hooks
export const useScanData = () => {
  const { scanData, setScanData, clearScanData } = useScanStore();
  return { scanData, setScanData, clearScanData };
};

export const usePendingMealData = () => {
  const { pendingMealData, setPendingMealData, clearPendingMealData } = useScanStore();
  return { pendingMealData, setPendingMealData, clearPendingMealData };
};
