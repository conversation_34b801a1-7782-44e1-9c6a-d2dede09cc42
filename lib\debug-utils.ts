/**
 * Debug utilities for image handling and troubleshooting
 */

/**
 * Logs detailed information about an image URI for debugging
 * @param uri - The image URI to debug
 * @param context - Context information (e.g., "scan-results", "meal-card")
 */
export const debugImageUri = (uri: string, context: string = 'unknown') => {
  console.log(`[DEBUG] Image URI Debug - Context: ${context}`);
  console.log(`[DEBUG] URI: ${uri}`);
  console.log(`[DEBUG] URI Length: ${uri?.length || 0}`);
  console.log(`[DEBUG] Has data: prefix: ${uri?.startsWith('data:')}`);
  console.log(`[DEBUG] Has base64 content: ${uri?.includes('base64,')}`);
  
  if (uri?.startsWith('data:')) {
    const mimeMatch = uri.match(/^data:([^;]+)/);
    const mimeType = mimeMatch ? mimeMatch[1] : 'unknown';
    console.log(`[DEBUG] MIME Type: ${mimeType}`);
    
    const base64Index = uri.indexOf(',');
    if (base64Index !== -1) {
      const base64Part = uri.substring(base64Index + 1);
      console.log(`[DEBUG] Base64 Length: ${base64Part.length}`);
      console.log(`[DEBUG] Base64 Preview: ${base64Part.substring(0, 50)}...`);
    }
  }
  
  console.log(`[DEBUG] ==========================================`);
};

/**
 * Validates if an image URI is properly formatted for React Native
 * @param uri - The image URI to validate
 * @returns Object with validation results
 */
export const validateImageUri = (uri: string) => {
  const result = {
    isValid: false,
    hasDataPrefix: false,
    hasMimeType: false,
    hasBase64Content: false,
    mimeType: null as string | null,
    errors: [] as string[],
  };

  if (!uri) {
    result.errors.push('URI is empty or null');
    return result;
  }

  if (uri.startsWith('data:')) {
    result.hasDataPrefix = true;
    
    const mimeMatch = uri.match(/^data:([^;]+)/);
    if (mimeMatch) {
      result.hasMimeType = true;
      result.mimeType = mimeMatch[1];
    } else {
      result.errors.push('Invalid MIME type in data URI');
    }
    
    if (uri.includes('base64,')) {
      result.hasBase64Content = true;
      const base64Index = uri.indexOf(',');
      const base64Part = uri.substring(base64Index + 1);
      
      if (base64Part.length === 0) {
        result.errors.push('Base64 content is empty');
      }
    } else {
      result.errors.push('Missing base64 content in data URI');
    }
  } else if (uri.startsWith('http://') || uri.startsWith('https://')) {
    // HTTP URLs are valid
    result.isValid = true;
  } else if (uri.startsWith('file://')) {
    // File URLs are valid
    result.isValid = true;
  } else {
    result.errors.push('URI does not have a valid protocol (data:, http:, https:, file:)');
  }

  result.isValid = result.errors.length === 0;
  return result;
};

/**
 * Logs scan data for debugging purposes
 * @param scanData - The scan data object
 * @param context - Context information
 */
export const debugScanData = (scanData: any, context: string = 'unknown') => {
  console.log(`[DEBUG] Scan Data Debug - Context: ${context}`);
  console.log(`[DEBUG] Scan Data:`, {
    id: scanData?.id,
    food_name: scanData?.food_name,
    imageURI: scanData?.imageURI ? `${scanData.imageURI.substring(0, 50)}...` : 'null',
    hasImageURI: !!scanData?.imageURI,
    macrosCount: scanData?.macros?.length || 0,
  });
  
  if (scanData?.imageURI) {
    debugImageUri(scanData.imageURI, `${context}-scanData`);
  }
};
