import { SecureStorage } from '@/lib/utils';
import { Redirect } from 'expo-router';
import { useEffect, useState } from 'react';
import { ActivityIndicator, View } from 'react-native';

export default function WelcomeScreen() {
  const [href, setHref] = useState<string | null>(null);

  useEffect(() => {
    const checkUser = async () => {
      const user = await SecureStorage.getUserProfile();

      if (user && user.is_onboarded) {
        setHref('/dashboard');
      } else if (user && !user.is_onboarded) {
        setHref('/onboarding-1');
      } else {
        setHref('/auth');
      }
    };

    checkUser();
  }, [href]);

  if (!href) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator size="large" />
      </View>
    );
  }

  return <Redirect href={href} />;
}
