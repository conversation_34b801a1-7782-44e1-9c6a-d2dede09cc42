export interface AddToLogPayload {
  nutrition_label: number;
  consumed: boolean;
  quantity?: number;
  metric?: 'g' | 'mg';
}

export interface AddToLogResponse {
  original_base64: any;
  nutrition_label: NutritionLabel;
  is_logged: boolean;
  is_consumed: boolean;
  consumed_quantity: number;
  consumed_metric: string;
  consumed_nutrition: ConsumedNutrition;
  logged_at: string;
  created_at: string;
}

export interface NutritionLabel {
  id: number;
  food_name: string;
  brand: any;
  serving: Serving;
  calories: number;
  macros: Macros;
  micros: Micros;
  source: string;
  is_eatable: string;
  advice: string;
}

export interface Serving {
  unit: string;
  quantity: number;
  weight_grams: number;
}

export interface Macros {
  fat_g: number;
  carbs_g: number;
  fiber_g: number;
  sugar_g: number;
  protein_g: number;
  saturated_fat_g: number;
  unsaturated_fat_g: number;
}

export interface Micros {
  iron_mg: number;
  sodium_mg: number;
  calcium_mg: number;
  potassium_mg: number;
  vitamin_c_mg: number;
  cholesterol_mg: number;
}

export interface ConsumedNutrition {
  macros: Macros2;
  micros: Micros2;
  serving: Serving2;
  calories: number;
}

export interface Macros2 {
  fat_g: number;
  carbs_g: number;
  fiber_g: number;
  sugar_g: number;
  protein_g: number;
  saturated_fat_g: number;
  unsaturated_fat_g: number;
}

export interface Micros2 {
  iron_mg: number;
  sodium_mg: number;
  calcium_mg: number;
  potassium_mg: number;
  vitamin_c_mg: number;
  cholesterol_mg: number;
}

export interface Serving2 {
  unit: string;
  quantity: number;
  weight_grams: number;
}
