import { Picker, PickerItemProps, PickerProps } from '@react-native-picker/picker';

const Select = ({ itemStyle, ...props }: PickerProps) => {
  return (
    <Picker
      itemStyle={[{ fontFamily: 'AlanSans_400Regular', color: 'black', fontSize: 16 }, itemStyle]}
      {...props}>
      {props.children}
    </Picker>
  );
};

const Option = ({ ...props }: PickerItemProps) => {
  return <Picker.Item {...props} />;
};

export { Select, Option };
