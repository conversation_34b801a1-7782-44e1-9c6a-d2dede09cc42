import apiClient from '@/lib/axios';
import { useMutation, UseMutationOptions } from '@tanstack/react-query';

interface LogoutPayload {
  refresh: string | null;
}

const useLogout = (options?: Omit<UseMutationOptions<void, Error, LogoutPayload>, 'mutationFn'>) =>
  useMutation({
    mutationFn: async ({ refresh }: LogoutPayload) => {
      const res = await apiClient.post('/api/v1/auth/logout/', { refresh });
      return res.data;
    },
    ...options,
  });

export { useLogout };
