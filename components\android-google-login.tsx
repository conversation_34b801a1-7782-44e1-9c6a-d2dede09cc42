import { useGoogleAuth } from '@/services/auth/google-auth';
import { GoogleAuthResponse } from '@/services/auth/types';
import Ionicons from '@expo/vector-icons/Ionicons';
import { router } from 'expo-router';
import { ActivityIndicator, View } from 'react-native';
import CustomButton from './ui/button';
import { CustomText } from './ui/text';

const AndroidGoogleSignIn = () => {
  const { mutate, isPending } = useGoogleAuth({
    onSuccess: handleSuccess,
  });

  function handleSuccess(data: GoogleAuthResponse) {
    const { user } = data;
    if (user.is_onboarded) {
      router.replace('/dashboard');
    } else {
      router.replace({
        pathname: '/onboarding-1',
        params: {
          data: JSON.stringify(user),
        },
      });
    }
  }

  function handleGoogleSignIn() {
    mutate();
  }

  return (
    <View className="w-full flex-1 flex-col items-center justify-center">
      <CustomButton
        buttonVariant="muted"
        disabled={isPending}
        className="flex h-12 w-full flex-row items-center justify-center gap-4"
        onPress={handleGoogleSignIn}>
        {isPending ? (
          <ActivityIndicator size="small" color="#e29478eb" />
        ) : (
          <Ionicons name="logo-google" size={24} color="#e29478eb" />
        )}
        <CustomText className="font-alansans ">
          {isPending ? 'Signing in...' : 'Connect with Google'}
        </CustomText>
      </CustomButton>
    </View>
  );
};

export default AndroidGoogleSignIn;
