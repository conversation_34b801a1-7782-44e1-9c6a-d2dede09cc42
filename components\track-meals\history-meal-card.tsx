import { CustomText } from '@/components/ui/text';
import { Ionicons } from '@expo/vector-icons';
import { Image, ScrollView, View, Pressable } from 'react-native';
import { MacroCard } from '../scan-and-check/macro-card';
import CustomButton from '../ui/button';
import { MealCardProps } from './types';
import { getMealType } from '@/lib/scan-history-utils';
import { useState } from 'react';

const getMealIcon = (mealType: string) => {
  switch (mealType) {
    case 'Breakfast':
      return 'sunny';
    case 'Lunch':
      return 'restaurant';
    case 'Dinner':
      return 'moon';
    case 'Snack':
    default:
      return 'fast-food';
  }
};

interface MealCardComponentProps extends MealCardProps {
  isUntracked?: boolean;
  onAddMeal?: () => void;
  onMealPress?: () => void;
}

export const MealCard = ({
  uri,
  mealName,
  calories,
  macros,
  timestamp,
  isUntracked,
  onAddMeal,
  onMealPress,
}: MealCardComponentProps) => {
  const [imageError, setImageError] = useState(false);

  const timeString = new Date(timestamp).toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
  });

  const mealType = getMealType(timestamp);

  return (
    <Pressable
      onPress={!isUntracked && onMealPress ? onMealPress : undefined}
      className="flex h-auto w-full flex-col items-start justify-between gap-4 overflow-hidden rounded-2xl bg-white shadow-sm">
      {/* Header Section */}
      <View className="flex w-full flex-row items-start justify-between gap-3 p-4 pb-0">
        <View className="relative">
          {!imageError && uri ? (
            <Image
              source={{ uri }}
              className="size-24 rounded-2xl"
              onError={() => setImageError(true)}
            />
          ) : (
            <View className="flex size-24 items-center justify-center rounded-2xl bg-muted">
              <Ionicons name="image-outline" size={24} color="#9ca3af" />
            </View>
          )}

          {!isUntracked && (
            <View className="absolute -bottom-2 -right-2 rounded-full bg-accent px-2 py-1 shadow-sm">
              <CustomText className="font-lexend text-xs font-bold text-white">
                {calories}
              </CustomText>
            </View>
          )}
          {isUntracked && (
            <View className="absolute -left-2 -top-2 rounded-full bg-orange-400 px-2 py-1 shadow-sm">
              <CustomText className="text-xs font-bold text-white">Untracked</CustomText>
            </View>
          )}
        </View>
        <View className="flex flex-1 flex-col items-start justify-start gap-2 pt-1">
          <CustomText variant={'h4'} className="text-primary">
            {mealName}
          </CustomText>
          <View className="flex flex-row items-center gap-3">
            <View className="flex flex-row items-center gap-1.5">
              <Ionicons name="time" size={16} color={'#006d77'} weight="bold" />
              <CustomText className="text-sm text-muted-foreground">{timeString}</CustomText>
            </View>
            <CustomText className="text-sm text-muted-foreground">•</CustomText>
            <View className="flex flex-row items-center gap-1.5">
              <Ionicons name={getMealIcon(mealType)} size={16} color={'#006d77'} weight="bold" />
              <CustomText className="text-sm text-muted-foreground">{mealType}</CustomText>
            </View>
          </View>
          {/* {isUntracked && onAddMeal && (
            <Pressable onPress={onAddMeal} className="mt-1 rounded-lg bg-accent px-3 py-1">
              <CustomText className="text-xs font-semibold text-white">Add to Log</CustomText>
            </Pressable>
          )} */}
        </View>
        {isUntracked && onAddMeal && (
          <CustomButton
            onPress={onAddMeal}
            buttonVariant="accent"
            className="flex h-10 w-10 items-center justify-center rounded-full">
            <Ionicons name="add" size={24} color="white" />
          </CustomButton>
        )}
      </View>

      {/* Macros Section */}
      {macros.length > 0 && (
        <View className="w-full rounded-xl border-t border-border px-4 py-2">
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerClassName="flex flex-row items-center justify-start gap-3">
            {macros.map((macro) => (
              <MacroCard key={macro.id} macro={macro} />
            ))}
          </ScrollView>
        </View>
      )}
    </Pressable>
  );
};
