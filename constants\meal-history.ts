import { MealCardProps } from '@/components/track-meals/types';

export const mealHistory: MealCardProps[] = [
  {
    uri: 'https://www.allrecipes.com/thmb/UgUZpaTRGWIHEk57yWMhMEjffiY=/1500x0/filters:no_upscale():max_bytes(150000):strip_icc()/16160-juicy-grilled-chicken-breasts-ddmfs-5594-hero-3x4-902673c819994c0191442304b40104af.jpg',
    macros: [
      {
        id: 1,
        key: 'protein_g',
        name: 'Protein',
        value: 100,
      },
    ],
    timestamp: new Date('2025-10-21T10:00:00').getTime(),
    calories: '350 kcal',
    mealName: 'Grilled Chicken',
  },
  {
    uri: 'https://www.allrecipes.com/thmb/UgUZpaTRGWIHEk57yWMhMEjffiY=/1500x0/filters:no_upscale():max_bytes(150000):strip_icc()/16160-juicy-grilled-chicken-breasts-ddmfs-5594-hero-3x4-902673c819994c0191442304b40104af.jpg',
    macros: [
      {
        id: 1,
        key: 'protein_g',
        name: 'Protein',
        value: 23,
      },
      {
        id: 2,
        key: 'carbs_g',
        name: 'Carbs',
        value: 100,
      },
      {
        id: 3,
        key: 'fiber_g',
        name: 'Fiber',
        value: 30,
      },
      {
        id: 4,
        key: 'fat_g',
        name: 'Fats',
        value: 40,
      },
    ],
    timestamp: new Date('2025-10-21T13:30:00').getTime(),
    calories: '350 kcal',
    mealName: 'Chicken Salad',
  },
  {
    uri: 'https://www.allrecipes.com/thmb/UgUZpaTRGWIHEk57yWMhMEjffiY=/1500x0/filters:no_upscale():max_bytes(150000):strip_icc()/16160-juicy-grilled-chicken-breasts-ddmfs-5594-hero-3x4-902673c819994c0191442304b40104af.jpg',
    macros: [
      {
        id: 1,
        key: 'protein_g',
        name: 'Protein',
        value: 50,
      },
      {
        id: 2,
        key: 'carbs_g',
        name: 'Carbs',
        value: 80,
      },
    ],
    timestamp: new Date('2025-10-21T18:45:00').getTime(),
    calories: '450 kcal',
    mealName: 'Steak and Potatoes',
  },
  {
    uri: 'https://www.allrecipes.com/thmb/UgUZpaTRGWIHEk57yWMhMEjffiY=/1500x0/filters:no_upscale():max_bytes(150000):strip_icc()/16160-juicy-grilled-chicken-breasts-ddmfs-5594-hero-3x4-902673c819994c0191442304b40104af.jpg',
    macros: [
      {
        id: 1,
        key: 'protein_g',
        name: 'Protein',
        value: 30,
      },
      {
        id: 2,
        key: 'carbs_g',
        name: 'Carbs',
        value: 60,
      },
      {
        id: 3,
        key: 'fiber_g',
        name: 'Fiber',
        value: 10,
      },
    ],
    timestamp: new Date('2025-10-20T09:00:00').getTime(),
    calories: '300 kcal',
    mealName: 'Oatmeal with Berries',
  },
  {
    uri: 'https://www.allrecipes.com/thmb/UgUZpaTRGWIHEk57yWMhMEjffiY=/1500x0/filters:no_upscale():max_bytes(150000):strip_icc()/16160-juicy-grilled-chicken-breasts-ddmfs-5594-hero-3x4-902673c819994c0191442304b40104af.jpg',
    macros: [
      {
        id: 1,
        key: 'protein_g',
        name: 'Protein',
        value: 40,
      },
      {
        id: 2,
        key: 'carbs_g',
        name: 'Carbs',
        value: 20,
      },
    ],
    timestamp: new Date('2025-10-20T12:30:00').getTime(),
    calories: '250 kcal',
    mealName: 'Tuna Sandwich',
  },
  {
    uri: 'https://www.allrecipes.com/thmb/UgUZpaTRGWIHEk57yWMhMEjffiY=/1500x0/filters:no_upscale():max_bytes(150000):strip_icc()/16160-juicy-grilled-chicken-breasts-ddmfs-5594-hero-3x4-902673c819994c0191442304b40104af.jpg',
    macros: [
      {
        id: 1,
        key: 'protein_g',
        name: 'Protein',
        value: 25,
      },
      {
        id: 2,
        key: 'carbs_g',
        name: 'Carbs',
        value: 50,
      },
      {
        id: 3,
        key: 'fiber_g',
        name: 'Fiber',
        value: 15,
      },
    ],
    timestamp: new Date('2025-10-19T19:00:00').getTime(),
    calories: '400 kcal',
    mealName: 'Pasta with Veggies',
  },
];
