import apiClient from '@/lib/axios';
import { useMutation, UseMutationOptions } from '@tanstack/react-query';
import { AddToLogPayload, AddToLogResponse } from './types';

export const useAddToLog = (
  options?: Omit<UseMutationOptions<AddToLogResponse, Error, AddToLogPayload>, 'mutationFn'>
) =>
  useMutation({
    mutationFn: async (data) => {
      const res = await apiClient.post('/api/v1/scan/nutrition-label/log-meal/', data);
      return res.data;
    },
    ...options,
  });
