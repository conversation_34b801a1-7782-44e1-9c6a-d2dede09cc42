import { useCallback, useEffect, useState } from 'react';
import { View, Platform } from 'react-native';
import { CustomText } from '@/components/ui/text';
import { Stack } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { QueryClientProvider } from '@tanstack/react-query';

import '../global.css';
import { queryClient } from '../lib/query-client';
import useMyFonts from '../lib/fonts';

let GoogleSignin: any;

// if (Platform.OS === 'android') {
//   import('@react-native-google-signin/google-signin').then((module) => {
//     GoogleSignin = module.GoogleSignin;
//   });
// }

// DO NOT CHANGE THIS TO IMPORT SYNTAX, DEV SERVER WONT WORK !!
if (Platform.OS === 'android') {
  const googleSigninModule = require('@react-native-google-signin/google-signin');
  GoogleSignin = googleSigninModule.GoogleSignin;
}

void SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const {
    poppinsFontsLoaded,
    poppinsError,
    lexendDecaFontsLoaded,
    lexendDecaError,
    alanSansFontsLoaded,
    alanSansError,
  } = useMyFonts();

  const [isAppReady, setIsAppReady] = useState(false);

  const allFontsLoaded = poppinsFontsLoaded && lexendDecaFontsLoaded && alanSansFontsLoaded;
  const anyFontError = poppinsError || lexendDecaError || alanSansError;

  useEffect(() => {
    if (allFontsLoaded) {
      setIsAppReady(true);
    }
  }, [allFontsLoaded]);

  useEffect(() => {
    if (Platform.OS === 'android') {
      GoogleSignin.configure({
        webClientId: '53716432007-c75shp0mfb1c4uvc9qv4fbni084vsjki.apps.googleusercontent.com',
        offlineAccess: true,
        profileImageSize: 100,
      });
    }
  }, []);

  const onLayoutRootView = useCallback(async () => {
    if (isAppReady) {
      try {
        await SplashScreen.hideAsync();
      } catch (error) {
        console.warn('Failed to hide splash screen:', error);
      }
    }
  }, [isAppReady]);

  if (anyFontError) {
    return (
      <SafeAreaProvider>
        <View
          style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}
          onLayout={onLayoutRootView}>
          <CustomText
            style={{
              fontSize: 18,
              color: 'red',
              textAlign: 'center',
              marginHorizontal: 24,
            }}>
            Something went wrong while loading fonts. Please restart the app.
          </CustomText>
        </View>
      </SafeAreaProvider>
    );
  }

  if (!isAppReady) {
    return null;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <SafeAreaProvider className="bg-background">
        <View style={{ flex: 1 }} onLayout={onLayoutRootView}>
          <Stack
            screenOptions={{
              headerShown: false,
              animation: 'slide_from_bottom',
            }}
          />
        </View>
      </SafeAreaProvider>
    </QueryClientProvider>
  );
}
