@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: #edf6f9;
    --foreground: #361d0e;
    --card: #ffffff;
    --card-foreground: #000;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: #006d77;
    --tertiary: #83c5be;
    --primary-foreground: #edf6f9;
    --secondary: #83c5be;
    --secondary-foreground: #361d0e;
    --muted: #ffddd2;
    --muted-foreground: #666;
    --accent: #e29578;
    --accent-foreground: #361d0e;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;
    --border: #d6e6e8;
    --input: #d6e6e8;
    --ring: #83c5be;
    --radius: 0.625rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    --font-poppins: 'Poppins_400Regular';

    --font-poppins-thin: 'Poppins_100Thin';
    --font-poppins-thin-italic: 'Poppins_100Thin_Italic';
    --font-poppins-extralight: 'Poppins_200ExtraLight';
    --font-poppins-extralight-italic: 'Poppins_200ExtraLight_Italic';
    --font-poppins-light: 'Poppins_300Light';
    --font-poppins-light-italic: 'Poppins_300Light_Italic';
    --font-poppins-regular: 'Poppins_400Regular';
    --font-poppins-italic: 'Poppins_400Regular_Italic';
    --font-poppins-medium: 'Poppins_500Medium';
    --font-poppins-medium-italic: 'Poppins_500Medium_Italic';
    --font-poppins-semibold: 'Poppins_600SemiBold';
    --font-poppins-semibold-italic: 'Poppins_600SemiBold_Italic';
    --font-poppins-bold: 'Poppins_700Bold';
    --font-poppins-bold-italic: 'Poppins_700Bold_Italic';
    --font-poppins-extrabold: 'Poppins_800ExtraBold';
    --font-poppins-extrabold-italic: 'Poppins_800ExtraBold_Italic';
    --font-poppins-black: 'Poppins_900Black';
    --font-poppins-black-italic: 'Poppins_900Black_Italic';

    --font-lexend: 'LexendDeca_400Regular';

    --font-lexend-thin: 'LexendDeca_100Thin';
    --font-lexend-extralight: 'LexendDeca_200ExtraLight';
    --font-lexend-light: 'LexendDeca_300Light';
    --font-lexend-regular: 'LexendDeca_400Regular';
    --font-lexend-medium: 'LexendDeca_500Medium';
    --font-lexend-semibold: 'LexendDeca_600SemiBold';
    --font-lexend-bold: 'LexendDeca_700Bold';
    --font-lexend-extrabold: 'LexendDeca_800ExtraBold';
    --font-lexend-black: 'LexendDeca_900Black';

    --font-alansans: 'AlanSans_400Regular';

    --font-alansans-light: 'AlanSans_300Light';
    --font-alansans-regular: 'AlanSans_400Regular';
    --font-alansans-medium: 'AlanSans_500Medium';
    --font-alansans-semibold: 'AlanSans_600SemiBold';
    --font-alansans-bold: 'AlanSans_700Bold';
    --font-alansans-extrabold: 'AlanSans_800ExtraBold';
    --font-alansans-black: 'AlanSans_900Black';
  }
}
