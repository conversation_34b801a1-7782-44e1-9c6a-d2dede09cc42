import apiClient from '@/lib/axios';
import { useMutation, UseMutationOptions } from '@tanstack/react-query';

const useNutritionRecalculate = (
  options?: Omit<UseMutationOptions<void, Error, void>, 'mutationFn'>
) =>
  useMutation({
    mutationFn: async () => {
      const res = await apiClient.post('/api/v1/user/nutrition/recalculate/');
      return res.data;
    },
    ...options,
  });

export { useNutritionRecalculate };
