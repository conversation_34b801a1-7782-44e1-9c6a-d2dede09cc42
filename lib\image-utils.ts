/**
 * Utility functions for handling image URIs and base64 formatting
 */

/**
 * Ensures a base64 string has the proper data:image header for React Native Image components
 * @param base64String - The base64 string (with or without data:image header)
 * @param mimeType - The MIME type (default: 'image/png')
 * @returns Properly formatted data URI
 */
export const ensureDataImageHeader = (base64String: string, mimeType: string = 'image/png'): string => {
  if (!base64String) {
    return '';
  }

  // If it already has a data: header, return as is
  if (base64String.startsWith('data:')) {
    return base64String;
  }

  // Add the data:image header
  return `data:${mimeType};base64,${base64String}`;
};

/**
 * Removes the data:image header from a base64 string to get raw base64
 * @param dataUri - The data URI string
 * @returns Raw base64 string without header
 */
export const extractBase64FromDataUri = (dataUri: string): string => {
  if (!dataUri) {
    return '';
  }

  // If it doesn't have a data: header, return as is
  if (!dataUri.startsWith('data:')) {
    return dataUri;
  }

  // Extract the base64 part after the comma
  const base64Index = dataUri.indexOf(',');
  if (base64Index === -1) {
    return dataUri;
  }

  return dataUri.substring(base64Index + 1);
};

/**
 * Validates if a string is a valid base64 format
 * @param str - String to validate
 * @returns Boolean indicating if string is valid base64
 */
export const isValidBase64 = (str: string): boolean => {
  if (!str) {
    return false;
  }

  try {
    // Remove data URI header if present
    const base64String = extractBase64FromDataUri(str);
    
    // Check if it's valid base64
    return btoa(atob(base64String)) === base64String;
  } catch (err) {
    return false;
  }
};

/**
 * Gets the MIME type from a data URI
 * @param dataUri - The data URI string
 * @returns MIME type or null if not found
 */
export const getMimeTypeFromDataUri = (dataUri: string): string | null => {
  if (!dataUri || !dataUri.startsWith('data:')) {
    return null;
  }

  const mimeMatch = dataUri.match(/^data:([^;]+)/);
  return mimeMatch ? mimeMatch[1] : null;
};
