import { CustomText } from '@/components/ui/text';
import { Image, ScrollView, View } from 'react-native';
import { FoodCardProps } from './types';
import { MacroCard } from './macro-card';
import { useState } from 'react';
import { Ionicons } from '@expo/vector-icons';

export const FoodCard = ({ image, foodName, calories, macros }: FoodCardProps) => {
  const [imageError, setImageError] = useState(false);

  return (
    <View className="overflow-hidden rounded-2xl bg-card shadow-sm">
      {!imageError && image ? (
        <Image
          source={{ uri: image }}
          className="h-40 w-full"
          resizeMode="cover"
          onError={() => setImageError(true)}
        />
      ) : (
        <View className="flex h-40 w-full items-center justify-center bg-muted">
          <Ionicons name="image-outline" size={48} color="#9ca3af" />
          <CustomText className="mt-2 text-sm text-muted-foreground">
            Image not available
          </CustomText>
        </View>
      )}
      <View className="w-full p-4">
        <CustomText variant="h3" className="mb-1 font-poppins text-primary">
          {foodName}
        </CustomText>
        <CustomText className="mb-2 font-poppins text-xl">
          {calories}
          <CustomText className="font-poppins text-muted-foreground"> Calories</CustomText>
        </CustomText>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View className="w-auto flex-row items-start justify-start gap-3">
            {macros.map((macro) => (
              <MacroCard key={macro.key} macro={macro} />
            ))}
          </View>
        </ScrollView>
      </View>
    </View>
  );
};
