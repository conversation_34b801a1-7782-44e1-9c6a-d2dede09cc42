import { Stack } from 'expo-router';

const InnerScreenLayout = () => {
  return (
    <Stack>
      <Stack.Screen
        name="scan-results"
        options={{
          headerTitle: 'Food Scan Results',
          headerBackTitle: 'Back',
          headerTitleStyle: {
            fontFamily: 'Poppins_400Regular',
          },
          headerBackTitleStyle: {
            fontFamily: 'LexendDeca_400Regular',
          },
        }}
      />
      <Stack.Screen
        name="add-to-log"
        options={{
          headerTitle: 'Add to Log',
          headerBackTitle: 'Back',
          headerTitleStyle: {
            fontFamily: 'Poppins_400Regular',
          },
          headerBackTitleStyle: {
            fontFamily: 'LexendDeca_400Regular',
          },
        }}
      />
      <Stack.Screen
        name="badges-and-acheivements"
        options={{
          headerTitle: 'Badges And Acheivements',
          headerBackTitle: 'Back',
          headerTitleStyle: {
            fontFamily: 'Poppins_400Regular',
          },
          headerBackTitleStyle: {
            fontFamily: 'LexendDeca_400Regular',
          },
        }}
      />
    </Stack>
  );
};

export default InnerScreenLayout;
