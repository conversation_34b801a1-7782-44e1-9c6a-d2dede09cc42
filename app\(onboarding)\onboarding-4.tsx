import NextButton from '@/components/onboarding/next-button';
import OnboardingBars from '@/components/onboarding/onboarding-bars';
import Form from '@/components/ui/form';
import SafeAreaContainer from '@/components/ui/safe-area-container';
import { CustomText } from '@/components/ui/text';
import { COMMON_TOAST_PROPS } from '@/constants/toast-common';
import { useNutritionRecalculate } from '@/services/auth/calculate-user-nutrition';
import { useGetMe } from '@/services/auth/me';
import { router } from 'expo-router';
import { Image, View } from 'react-native';
import { showToast } from 'react-native-nitro-toast';

const OnBoarding = () => {
  const { data } = useGetMe({});
  const userName = `Welcome ${data?.first_name} ${data?.last_name}!`;

  const { mutate, isPending } = useNutritionRecalculate({
    onSuccess() {
      showToast('Onboarding completed successfully!', {
        type: 'success',
        title: userName,
        ...COMMON_TOAST_PROPS,
      });
      router.replace('/dashboard');
    },
    onError(error) {
      showToast('Soemthing went wrong during onboarding! Please try again.' + error.message, {
        type: 'error',
        ...COMMON_TOAST_PROPS,
      });
    },
  });

  const handleFinish = () => mutate();

  return (
    <SafeAreaContainer className="flex flex-1 items-center justify-start pt-0">
      <Form>
        <View className="max-h-screen-safe flex-1">
          <OnboardingBars barsFilled={4} />
          <View className="gap-y-3">
            <CustomText className="ios:text-5xl ios:pt-3 self-start font-poppins-bold text-3xl text-foreground">
              You&apos;re all set!
            </CustomText>
            <CustomText className="self-start font-lexend text-lg text-accent-foreground">
              Welcome to Eatable! Let&apos;s start your journey to better eating habits.
            </CustomText>
          </View>
          <Image
            source={require('@/assets/onboarding-complete.png')}
            className="mx-auto mt-12 w-full"
            resizeMode="contain"
          />
          <NextButton
            disabled={isPending}
            onPress={handleFinish}
            buttonTitle={isPending ? 'Loading...' : 'Get Started'}
          />
        </View>
      </Form>
    </SafeAreaContainer>
  );
};

export default OnBoarding;
