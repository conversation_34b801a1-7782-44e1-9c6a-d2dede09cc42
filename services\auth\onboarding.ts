import { useMutation, UseMutationOptions } from '@tanstack/react-query';
import { OnboardingPayload, OnboardingResponse } from './types';
import apiClient from '@/lib/axios';

const useOnboarding = (
  options?: Omit<
    UseMutationOptions<OnboardingResponse, Error, Partial<OnboardingPayload>>,
    'mutationFn'
  >
) =>
  useMutation({
    mutationFn: async (data: Partial<OnboardingPayload>) => {
      const res = await apiClient.patch('/api/v1/user/onboarding/', data);
      return res.data;
    },
    ...options,
  });

export { useOnboarding };
