import { Macro } from '@/components/scan-and-check/types';
import { MealCardProps } from '@/components/track-meals/types';
import { Macros, ScanHistoryItem } from '@/services/scan/types';

const macroName = (key: string) => {
  // Remove '_g' suffix
  let name = key.replace(/_g$/, '');
  // Replace underscores with spaces
  name = name.replace(/_/g, ' ');
  // Capitalize each word
  name = name.replace(/\b\w/g, (char) => char.toUpperCase());
  // Replace "Fat" with "Fats"
  name = name.replace(/\bFat\b/g, 'Fats');
  return name;
};

// Utility function to determine meal type based on timestamp
export const getMealType = (timestamp: number): string => {
  const date = new Date(timestamp);
  const hour = date.getHours();

  if (hour >= 4 && hour < 12) {
    return 'Breakfast';
  } else if (hour >= 12 && hour < 15) {
    return 'Lunch';
  } else if (hour >= 19 && hour < 24) {
    return 'Dinner';
  } else {
    return 'Snack';
  }
};

export const transformScanHistoryToMealCard = (
  item: ScanHistoryItem
): MealCardProps & { isUntracked: boolean; originalItem?: ScanHistoryItem } => {
  // Determine if meal is tracked or untracked based on is_consumed
  const isUntracked = !item.is_consumed;

  // Use consumed_nutrition for tracked meals, default nutrition_label for untracked
  let macrosData: Macros;
  let calories: number;

  if (isUntracked) {
    // For untracked meals, use the default macros from nutrition_label
    macrosData = item.nutrition_label.macros;
    calories = item.nutrition_label.calories;
  } else {
    // For tracked meals, use consumed_nutrition
    macrosData = item.consumed_nutrition?.macros || item.nutrition_label.macros;
    calories = item.consumed_nutrition?.calories || item.nutrition_label.calories;
  }

  // Convert macros object to array format expected by MealCard
  const macrosArray: Macro[] = Object.entries(macrosData)
    .filter(([, value]) => value && Number(value) > 0)
    .map(([key, value], index) => ({
      id: index,
      key: key as keyof Macros,
      name: macroName(key),
      value: Number(value),
    }));

  // Use the image URL from the backend response
  const imageUri = item.image_url;

  // Format calories
  const formattedCalories = `${calories} Cal`;

  return {
    uri: imageUri,
    mealName: item.nutrition_label.food_name,
    calories: formattedCalories,
    macros: macrosArray,
    timestamp: new Date(item.created_at).getTime(),
    isUntracked,
    // Store reference to original item for both tracked and untracked meals
    originalItem: item,
  };
};
