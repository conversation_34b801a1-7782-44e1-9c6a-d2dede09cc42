import { BadgeDetailed } from '@/components/badges';
import SafeAreaContainer from '@/components/ui/safe-area-container';
import { badgesDetailed } from '@/constants/badges';
import { ScrollView } from 'react-native';

// const badges = ['silverware-fork-knife', 'fire'];

const BadgesAndAcheivements = () => {
  return (
    <SafeAreaContainer className="w-full pt-0">
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerClassName="w-full flex flex-col items-center justify-between gap-3">
        {badgesDetailed.map((item, index) => (
          <BadgeDetailed key={index} {...item} />
        ))}
      </ScrollView>
    </SafeAreaContainer>
  );
};

export default BadgesAndAcheivements;
