{"name": "eatable", "version": "1.0.0", "scripts": {"android": "expo run:android", "build:android": "cd android && gradlew assembleRelease", "generate:sha": "cd android && gradlew signingReport", "ios": "expo run:ios", "start": "expo start", "prebuild": "expo prebuild", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\" && prettier -c \"**/*.{js,jsx,ts,tsx,json}\"", "format": "eslint \"**/*.{js,jsx,ts,tsx}\" --fix && prettier \"**/*.{js,jsx,ts,tsx,json}\" --write", "web": "expo start --web"}, "dependencies": {"@expo-google-fonts/alan-sans": "^0.4.0", "@expo-google-fonts/lexend-deca": "^0.4.1", "@expo-google-fonts/poppins": "^0.4.1", "@expo/vector-icons": "^15.0.2", "@hookform/resolvers": "^5.2.2", "@react-native-google-signin/google-signin": "^16.0.0", "@react-native-picker/picker": "^2.11.4", "@tanstack/react-query": "^5.90.2", "axios": "^1.12.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "expo": "54.0.12", "expo-camera": "~17.0.8", "expo-font": "~14.0.8", "expo-image-manipulator": "^14.0.7", "expo-image-picker": "^17.0.8", "expo-router": "~6.0.10", "expo-secure-store": "~15.0.7", "expo-splash-screen": "~31.0.10", "expo-status-bar": "~3.0.8", "expo-system-ui": "~6.0.7", "nativewind": "4.1.21", "react": "19.1.0", "react-hook-form": "^7.64.0", "react-native": "0.81.4", "react-native-nitro-modules": "^0.31.1", "react-native-nitro-toast": "^1.2.5", "react-native-progress-wheel": "^2.1.0", "react-native-reanimated": "~4.1.2", "react-native-safe-area-context": "5.6.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^4.1.12", "zustand": "^5.0.8"}, "devDependencies": {"@babel/core": "^7.28.4", "@types/react": "~19.1.17", "eslint": "^9.37.0", "eslint-config-expo": "~10.0.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-react-compiler": "19.1.0-rc.2", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.5.14", "tailwindcss": "^3.4.18", "typescript": "~5.9.2"}, "main": "expo-router/entry", "private": true}