import { View } from 'react-native';
import { CustomText } from '@/components/ui/text';
import { Ionicons } from '@expo/vector-icons';
import { cn } from '@/lib/utils';
import { FC } from 'react';

export interface EatableInfoBoxProps {
  type: 'success' | 'warning';
  title: string;
  message: string;
}

export const EatableInfoBox: FC<EatableInfoBoxProps> = ({ type, title, message }) => {
  const isSuccess = type === 'success';

  return (
    <View
      className={cn(
        'mt-4 flex-row items-start rounded-xl border p-4',
        isSuccess ? 'bg-secondary/20 border-secondary' : 'border-accent bg-muted'
      )}>
      <Ionicons
        name={isSuccess ? 'checkmark-circle' : 'warning'}
        size={22}
        color={isSuccess ? '#006D77' : '#E29578'}
        className="mt-0.5"
      />
      <View className="ml-3 flex-1">
        <CustomText variant="large" className={isSuccess ? 'text-primary' : 'text-accent'}>
          Eatable? {title}
        </CustomText>
        <CustomText className="mt-1 text-sm text-muted-foreground">{message}</CustomText>
      </View>
    </View>
  );
};
