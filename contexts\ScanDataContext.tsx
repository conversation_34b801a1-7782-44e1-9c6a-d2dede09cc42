import React, { createContext, useContext, useState, ReactNode } from 'react';
import { ScanResponse } from '@/services/scan/types';
import { Macro } from '@/components/scan-and-check/types';

interface ScanData extends Omit<ScanResponse, 'macros'> {
  macros: Macro[];
  imageURI?: string; // Local URI for display (backward compatibility)
}

interface ScanDataContextType {
  scanData: ScanData | null;
  setScanData: (data: ScanData | null) => void;
  clearScanData: () => void;
}

const ScanDataContext = createContext<ScanDataContextType | undefined>(undefined);

export const ScanDataProvider = ({ children }: { children: ReactNode }) => {
  const [scanData, setScanData] = useState<ScanData | null>(null);

  const clearScanData = () => setScanData(null);

  return (
    <ScanDataContext.Provider value={{ scanData, setScanData, clearScanData }}>
      {children}
    </ScanDataContext.Provider>
  );
};

export const useScanData = () => {
  const context = useContext(ScanDataContext);
  if (context === undefined) {
    throw new Error('useScanData must be used within a ScanDataProvider');
  }
  return context;
};
