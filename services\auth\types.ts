export interface GoogleAuthResponse {
  access: string;
  refresh: string;
  user: User;
}

export interface User {
  email: string;
  first_name: string;
  last_name: string;
  is_onboarded: boolean;
}

export interface OnboardedUser {
  activity_level: string;
  age: number;
  bmi: string;
  daily_calories: number;
  daily_carbs_g: string;
  daily_fat_g: string;
  daily_protein_g: string;
  dietary_preference: string;
  email: string;
  first_name: string;
  food_intolerances: string;
  gender: string;
  goal: string;
  height: string;
  id: number;
  is_active: boolean;
  is_onboarded: boolean;
  last_login: string | null;
  last_name: string;
  weight: string;
}

export interface OnboardingPayload {
  age: number;
  gender: 'male' | 'female' | 'others';
  height: string;
  weight: string;
  dietary_preference: 'vegetarian' | 'non_vegetarian';
  goal: 'eat_healthy' | 'lose_weight' | 'gain_muscle' | 'maintain_weight';
  food_intolerances: string;
  activity_level:
    | 'sedentary'
    | 'lightly_active'
    | 'moderately_active'
    | 'very_active'
    | 'extra_active';
}

export interface OnboardingResponse {
  age: number;
  gender: string;
  height: string;
  weight: string;
  bmi: string;
  dietary_preference: string;
  goal: string;
  food_intolerances: string;
}
