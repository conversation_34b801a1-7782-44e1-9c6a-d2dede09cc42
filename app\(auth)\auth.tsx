import AndroidGoogleSignIn from '@/components/android-google-login';
import Form from '@/components/ui/form';
import SafeAreaContainer from '@/components/ui/safe-area-container';
import { CustomText } from '@/components/ui/text';
import { Image, View } from 'react-native';

const AuthScreen = () => {
  //   const [authType, setAuthType] = useState<'login' | 'signup'>('login');
  return (
    <SafeAreaContainer className="scrollbar-hide mt-10 flex-1 bg-background">
      <Form>
        <View className="flex flex-1 items-center justify-start bg-background">
          <Image
            source={require('@/assets/auth.png')}
            className="h-[20vh] w-[45vw] rounded-3xl  shadow-2xl "
            resizeMode="contain"
          />
          <View className="mt-10 py-10">
            <CustomText className="ios:text-3xl text-center font-lexend-semibold text-2xl">
              Scan. Understand. Eat healthier with{''}
            </CustomText>
            <CustomText className="ios:pt-5 pt-3 text-center font-poppins-black text-5xl text-primary">
              &quot;Eatable?&quot;
            </CustomText>
          </View>
          {/* {authType === 'login' ? (
            <LoginForm OnAuthTypeChange={setAuthType} />
          ) : (
            <SignupForm OnAuthTypeChange={setAuthType} />
          )} */}
          <AndroidGoogleSignIn />
        </View>
      </Form>
    </SafeAreaContainer>
  );
};

export default AuthScreen;
