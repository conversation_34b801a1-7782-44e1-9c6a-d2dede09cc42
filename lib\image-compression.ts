/**
 * Image compression utilities for optimizing images before upload
 */

import { manipulateAsync, SaveFormat, ImageResult } from 'expo-image-manipulator';

export interface CompressionOptions {
  /** Maximum width in pixels (default: 1024) */
  maxWidth?: number;
  /** Maximum height in pixels (default: 1024) */
  maxHeight?: number;
  /** Compression quality 0-1 (default: 0.8) */
  quality?: number;
  /** Output format (default: JPEG for better compression) */
  format?: SaveFormat;
  /** Whether to maintain aspect ratio (default: true) */
  maintainAspectRatio?: boolean;
}

export interface CompressionResult {
  /** Compressed image URI */
  uri: string;
  /** Original file size in bytes (if available) */
  originalSize?: number;
  /** Compressed file size in bytes (if available) */
  compressedSize?: number;
  /** Compression ratio (if sizes available) */
  compressionRatio?: number;
  /** Original dimensions */
  originalDimensions?: { width: number; height: number } | null;
  /** Final dimensions */
  finalDimensions?: { width: number; height: number };
}

/**
 * Default compression settings optimized for food images
 */
const DEFAULT_OPTIONS: Required<CompressionOptions> = {
  maxWidth: 1024,
  maxHeight: 1024,
  quality: 0.8,
  format: SaveFormat.JPEG,
  maintainAspectRatio: true,
};

/**
 * Gets image dimensions from a URI
 */
const getImageDimensions = async (uri: string): Promise<{ width: number; height: number } | null> => {
  try {
    // Use manipulateAsync with empty actions to get image info
    const result = await manipulateAsync(uri, [], { format: SaveFormat.JPEG });
    // Note: expo-image-manipulator doesn't directly provide dimensions
    // This is a limitation we'll work around
    return null;
  } catch (error) {
    console.warn('Could not get image dimensions:', error);
    return null;
  }
};

/**
 * Calculates optimal resize dimensions while maintaining aspect ratio
 */
const calculateResizeDimensions = (
  originalWidth: number,
  originalHeight: number,
  maxWidth: number,
  maxHeight: number
): { width: number; height: number } => {
  const aspectRatio = originalWidth / originalHeight;
  
  let newWidth = originalWidth;
  let newHeight = originalHeight;
  
  // Check if resizing is needed
  if (originalWidth > maxWidth || originalHeight > maxHeight) {
    if (aspectRatio > 1) {
      // Landscape: width is larger
      newWidth = Math.min(maxWidth, originalWidth);
      newHeight = newWidth / aspectRatio;
      
      if (newHeight > maxHeight) {
        newHeight = maxHeight;
        newWidth = newHeight * aspectRatio;
      }
    } else {
      // Portrait: height is larger
      newHeight = Math.min(maxHeight, originalHeight);
      newWidth = newHeight * aspectRatio;
      
      if (newWidth > maxWidth) {
        newWidth = maxWidth;
        newHeight = newWidth / aspectRatio;
      }
    }
  }
  
  return {
    width: Math.round(newWidth),
    height: Math.round(newHeight),
  };
};

/**
 * Compresses an image with the specified options
 */
export const compressImage = async (
  imageUri: string,
  options: CompressionOptions = {}
): Promise<CompressionResult> => {
  const config = { ...DEFAULT_OPTIONS, ...options };
  
  try {
    console.log('🖼️ Starting image compression...', {
      uri: imageUri.substring(0, 50) + '...',
      options: config,
    });
    
    // Get original dimensions (if possible)
    const originalDimensions = await getImageDimensions(imageUri);
    
    // Prepare manipulation actions
    const actions = [];
    
    // Add resize action if dimensions are available and resizing is needed
    if (originalDimensions && config.maintainAspectRatio) {
      const newDimensions = calculateResizeDimensions(
        originalDimensions.width,
        originalDimensions.height,
        config.maxWidth,
        config.maxHeight
      );
      
      if (
        newDimensions.width < originalDimensions.width ||
        newDimensions.height < originalDimensions.height
      ) {
        actions.push({
          resize: {
            width: newDimensions.width,
            height: newDimensions.height,
          },
        });
      }
    } else {
      // Fallback: resize to max dimensions without knowing original size
      actions.push({
        resize: {
          width: config.maxWidth,
          height: config.maxHeight,
        },
      });
    }
    
    // Perform compression
    const result: ImageResult = await manipulateAsync(imageUri, actions, {
      compress: config.quality,
      format: config.format,
    });
    
    console.log('✅ Image compression completed', {
      originalUri: imageUri.substring(0, 50) + '...',
      compressedUri: result.uri.substring(0, 50) + '...',
      finalDimensions: { width: result.width, height: result.height },
    });
    
    return {
      uri: result.uri,
      originalDimensions,
      finalDimensions: { width: result.width, height: result.height },
    };
  } catch (error) {
    console.error('❌ Image compression failed:', error);
    throw new Error(`Image compression failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Compresses an image with preset configurations for different use cases
 */
export const compressImagePreset = {
  /**
   * High quality compression for food scanning (1024x1024, 85% quality)
   */
  foodScan: (imageUri: string) =>
    compressImage(imageUri, {
      maxWidth: 1024,
      maxHeight: 1024,
      quality: 0.85,
      format: SaveFormat.JPEG,
    }),
  
  /**
   * Medium quality compression for thumbnails (512x512, 75% quality)
   */
  thumbnail: (imageUri: string) =>
    compressImage(imageUri, {
      maxWidth: 512,
      maxHeight: 512,
      quality: 0.75,
      format: SaveFormat.JPEG,
    }),
  
  /**
   * Low quality compression for previews (256x256, 60% quality)
   */
  preview: (imageUri: string) =>
    compressImage(imageUri, {
      maxWidth: 256,
      maxHeight: 256,
      quality: 0.6,
      format: SaveFormat.JPEG,
    }),
  
  /**
   * Balanced compression for general use (800x800, 80% quality)
   */
  balanced: (imageUri: string) =>
    compressImage(imageUri, {
      maxWidth: 800,
      maxHeight: 800,
      quality: 0.8,
      format: SaveFormat.JPEG,
    }),
};

/**
 * Validates if an image URI is valid for compression
 */
export const validateImageUri = (uri: string): boolean => {
  if (!uri || typeof uri !== 'string') {
    return false;
  }
  
  // Check if it's a valid URI format
  const validPrefixes = ['file://', 'content://', 'ph://', 'assets-library://'];
  return validPrefixes.some(prefix => uri.startsWith(prefix)) || uri.startsWith('data:image/');
};

/**
 * Gets estimated file size reduction for compression settings
 */
export const estimateCompressionSavings = (quality: number, resizeRatio: number = 1): number => {
  // Rough estimation based on JPEG compression characteristics
  const qualityReduction = 1 - quality;
  const sizeReduction = qualityReduction * 0.7; // JPEG typically saves 70% of quality reduction
  const resizeReduction = 1 - (resizeRatio * resizeRatio); // Area reduction
  
  return Math.min(0.9, sizeReduction + resizeReduction); // Cap at 90% reduction
};
