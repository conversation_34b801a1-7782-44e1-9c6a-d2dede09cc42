import ImageCapturedPopup from '@/components/scan-and-check/scan-retake-dialog';
import CustomButton from '@/components/ui/button';
import SafeAreaContainer from '@/components/ui/safe-area-container';
import { CustomText } from '@/components/ui/text';
import { FOOD_FACTS } from '@/constants/constant-scan-results';
import { COMMON_TOAST_PROPS } from '@/constants/toast-common';
import { useScan } from '@/services/scan/scan';
import { CameraCapturedPicture, CameraView, useCameraPermissions } from 'expo-camera';
import { useFocusEffect, useRouter } from 'expo-router';
import { useCallback, useRef, useState } from 'react';
import { ActivityIndicator, Image, Linking, View } from 'react-native';
import { showToast } from 'react-native-nitro-toast';

import { Ionicons } from '@expo/vector-icons';
import { Asset } from 'expo-asset';
import * as ImagePicker from 'expo-image-picker';
import { compressImagePreset, validateImageUri } from '@/lib/image-compression';
import { useScanData } from '@/stores/scanStore';
import { getMacrosArray } from '@/lib/macro-name';

const EatableScan = () => {
  const [permission, requestPermission] = useCameraPermissions();
  const [continueModalVisible, setContinueModalVisible] = useState(false);
  const [photo, setPhoto] = useState<CameraCapturedPicture | undefined>(undefined);
  const [selectedGalleryImage, setSelectedGalleryImage] = useState<string | undefined>(undefined);
  const [cameraActive, setCameraActive] = useState(true);
  const [flashMode, setFlashMode] = useState<'off' | 'on'>('off');
  const [currentFact, setCurrentFact] = useState(FOOD_FACTS[0]);

  const router = useRouter();
  const { setScanData } = useScanData();

  const TEST_IMAGE = require('@/assets/chicken-biryani.jpg');
  const USE_TEST_IMAGE = false; // Set to false to use camera

  // Set a random fact when component mounts
  useFocusEffect(
    useCallback(() => {
      const randomIndex = Math.floor(Math.random() * FOOD_FACTS.length);
      setCurrentFact(FOOD_FACTS[randomIndex]);
    }, [])
  );

  useFocusEffect(
    useCallback(() => {
      if (!permission?.granted) {
        requestPermission();
      }
      setCameraActive(true);
      return () => {
        setCameraActive(false);
      };
    }, [permission?.granted, requestPermission])
  );

  const ref = useRef<CameraView>(null);

  const { mutate, isPending } = useScan({
    onSuccess(data) {
      let imageURI = '';
      if (selectedGalleryImage) {
        imageURI = selectedGalleryImage;
      } else if (photo?.uri) {
        imageURI = photo.uri;
      }

      // Transform macros to the expected format
      const macrosArray = getMacrosArray(data.macros);

      // Store scan data in Zustand store
      setScanData({
        ...data,
        macros: macrosArray,
        imageURI,
        image_url: data.image_url,
      });

      router.push('/scan-results');
    },
    onError(error) {
      showToast(error.message, {
        ...COMMON_TOAST_PROPS,
        type: 'error',
        title: error.name,
        duration: 14000,
      });
    },
  });

  const takePicture = async () => {
    if (USE_TEST_IMAGE) {
      // Skip camera and directly proceed with test image
      setContinueModalVisible(true);
      return;
    }

    try {
      const photo = await ref.current?.takePictureAsync({
        quality: 0.9, // High quality capture
        shutterSound: false,
      });
      if (photo?.uri) {
        setPhoto(photo);
        setContinueModalVisible(true);
      }
    } catch (error) {
      console.error('Error taking picture:', error);
      showToast('Failed to take picture', {
        ...COMMON_TOAST_PROPS,
        type: 'error',
        title: 'Camera Error',
      });
    }
  };

  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      showToast('Permission to access media library is required!', {
        ...COMMON_TOAST_PROPS,
        type: 'error',
        title: 'Permission Denied',
      });
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: 'images',
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.9, // Slight compression at picker level
    });

    if (!result.canceled && result.assets && result.assets.length > 0) {
      setSelectedGalleryImage(result.assets[0].uri);
      setContinueModalVisible(true);
    }
  };

  const handleProceed = async () => {
    setContinueModalVisible(false);
    // Set a new random fact when starting to scan
    const randomIndex = Math.floor(Math.random() * FOOD_FACTS.length);
    setCurrentFact(FOOD_FACTS[randomIndex]);

    try {
      let imageUri: string;

      if (USE_TEST_IMAGE) {
        // Use test image from assets
        const asset = Asset.fromModule(TEST_IMAGE);
        await asset.downloadAsync();
        imageUri = asset.localUri || asset.uri;
        console.log('Using test image:', imageUri);
      } else if (selectedGalleryImage) {
        // Use image from gallery
        imageUri = selectedGalleryImage;
      } else if (photo && photo.uri) {
        // Use camera photo
        imageUri = photo.uri;
      } else {
        showToast('No image available', {
          ...COMMON_TOAST_PROPS,
          type: 'error',
          title: 'Image Error',
        });
        return;
      }

      // Validate image URI
      if (!validateImageUri(imageUri)) {
        showToast('Invalid image format', {
          ...COMMON_TOAST_PROPS,
          type: 'error',
          title: 'Image Error',
        });
        return;
      }

      // Compress image before upload
      console.log('🔄 Compressing image for food scan...');
      const compressionResult = await compressImagePreset.foodScan(imageUri);

      console.log('✅ Image compressed successfully', {
        original: imageUri.substring(0, 50) + '...',
        compressed: compressionResult.uri.substring(0, 50) + '...',
        dimensions: compressionResult.finalDimensions,
      });

      // Send the compressed image URI to the scan service
      mutate({ imageUri: compressionResult.uri });
    } catch (error) {
      console.error('Error compressing image:', error);
      showToast('Failed to process image', {
        ...COMMON_TOAST_PROPS,
        type: 'error',
        title: 'Image Processing Error',
      });
    }
  };

  if (!permission) {
    return (
      <SafeAreaContainer className="flex-1 items-center justify-center bg-white">
        <CustomText variant="default">Checking camera permissions...</CustomText>
      </SafeAreaContainer>
    );
  }

  if (!permission.granted && !permission.canAskAgain) {
    return (
      <SafeAreaContainer className="h-auto w-full flex-1 items-center justify-center">
        <View className="flex w-full items-center justify-center gap-3 rounded-xl bg-card p-3">
          <CustomText className="text-center text-primary" variant="h4">
            This app needs permission to your camera.
          </CustomText>
          <CustomButton>
            <CustomText
              onPress={() => {
                if (permission.canAskAgain) {
                  requestPermission();
                } else {
                  Linking.openSettings();
                }
              }}
              variant={'buttonText'}>
              Give Camera Permission
            </CustomText>
          </CustomButton>
        </View>
      </SafeAreaContainer>
    );
  }

  return (
    <View className="flex-1">
      <View className="flex-1">
        {isPending ? (
          <View className="m-auto flex w-80 flex-col items-center justify-center rounded-xl">
            <ActivityIndicator size="large" />
            <View className="flex-row items-center justify-center">
              <CustomText className="mt-5 text-center font-lexend text-base leading-6 text-primary">
                {`"${currentFact.text}"`}
              </CustomText>
            </View>
          </View>
        ) : (
          <>
            <CameraView
              ref={ref}
              style={{ flex: 1 }}
              active={cameraActive}
              enableTorch={flashMode === 'on'}
              flash={flashMode}
            />
            <View className="absolute bottom-10 left-0 right-0 flex-row items-center justify-around">
              <CustomButton
                className="bg-tranparent mr-4 size-16 rounded-xl border-[1px] border-white"
                onPress={pickImage}>
                <Ionicons name={'images-outline'} size={24} color="white" />
              </CustomButton>
              <CustomButton
                className="bg-tranparent size-20 rounded-full border-[5px] border-white"
                onPress={takePicture}
              />
              <CustomButton
                className="bg-tranparent ml-4 size-16 rounded-xl border-[1px] border-white"
                onPress={() => setFlashMode(flashMode === 'off' ? 'on' : 'off')}>
                <Ionicons
                  name={flashMode === 'off' ? 'flashlight-outline' : 'flashlight'}
                  size={24}
                  color="white"
                />
              </CustomButton>
            </View>
          </>
        )}
        {continueModalVisible && (
          <View className="absolute bottom-8 left-3 flex-1 items-center justify-center bg-white">
            <Image
              className="border-2 border-white"
              source={USE_TEST_IMAGE ? TEST_IMAGE : { uri: selectedGalleryImage || photo?.uri }}
              style={{ width: 100, aspectRatio: 1 }}
            />
          </View>
        )}

        <ImageCapturedPopup
          visible={continueModalVisible}
          onProceed={handleProceed}
          onRetake={() => {
            setPhoto(undefined);
            setSelectedGalleryImage(undefined);
            setContinueModalVisible(false);
          }}
        />
      </View>
    </View>
  );
};

export default EatableScan;
