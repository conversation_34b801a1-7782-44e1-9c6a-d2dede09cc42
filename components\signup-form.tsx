import Ionicons from '@expo/vector-icons/Ionicons';
import { zodResolver } from '@hookform/resolvers/zod';
import { router } from 'expo-router';
import { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { View, TextInput } from 'react-native';
import * as z from 'zod';
import { AuthFormProps } from './types';
import CustomButton from './ui/button';
import { CustomText } from './ui/text';

const formSchema = z
  .object({
    email: z.email('Please enter a valid email address.'),
    password: z.string().min(8, 'Password must be at least 8 characters long.'),
    confirmPassword: z.string().min(8, 'Password must be at least 8 characters long.'),
  })
  .refine((props) => props.password.trim() === props.confirmPassword.trim(), {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

type FormSchema = z.infer<typeof formSchema>;

const SignupForm = ({ OnAuthTypeChange }: AuthFormProps) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
    },
  });

  const onSubmit = (data: FormSchema) => {
    console.log(data);
    router.replace('/(auth)/onboarding-1');
  };

  return (
    <View className="w-full flex-1 flex-col items-center justify-center">
      <Controller
        control={control}
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            className="h-12 w-full rounded-2xl bg-white placeholder:pl-5 placeholder:font-lexend-light placeholder:text-gray-400"
            placeholder="Enter Email"
            onBlur={onBlur}
            onChangeText={onChange}
            value={value}
          />
        )}
        name="email"
      />
      {errors.email && <CustomText variant={'error'}>{errors.email.message}</CustomText>}

      <View className="flex w-full flex-row items-center justify-center rounded-2xl bg-white pr-2.5">
        <Controller
          control={control}
          render={({ field: { onChange, onBlur, value } }) => (
            <TextInput
              className="h-12 flex-1 placeholder:pl-5 placeholder:font-lexend-light placeholder:text-gray-400"
              placeholder="Enter Password"
              onBlur={onBlur}
              onChangeText={onChange}
              value={value}
              secureTextEntry={!showPassword}
            />
          )}
          name="password"
        />
        <CustomButton
          className="w-fit bg-transparent p-2.5"
          onPress={() => setShowPassword((e) => !e)}>
          {showPassword ? (
            <Ionicons name="eye" size={24} color="#e29478eb" />
          ) : (
            <Ionicons name="eye-off-outline" size={24} color="#e29478eb" />
          )}
        </CustomButton>
      </View>
      {errors.password && <CustomText variant={'error'}>{errors.password.message}</CustomText>}

      <View className="flex w-full flex-row items-center justify-center rounded-2xl bg-white pr-2.5">
        <Controller
          control={control}
          render={({ field: { onChange, onBlur, value } }) => (
            <TextInput
              className="h-12 flex-1 placeholder:pl-5 placeholder:font-lexend-light placeholder:text-gray-400"
              placeholder="Confirm Password"
              onBlur={onBlur}
              onChangeText={onChange}
              value={value}
              secureTextEntry={!showConfirmPassword}
            />
          )}
          name="confirmPassword"
        />
        <CustomButton
          className="w-fit bg-transparent p-2.5"
          onPress={() => setShowConfirmPassword((e) => !e)}>
          {showConfirmPassword ? (
            <Ionicons name="eye" size={24} color="#e29478eb" />
          ) : (
            <Ionicons name="eye-off-outline" size={24} color="#e29478eb" />
          )}
        </CustomButton>
      </View>
      {errors.confirmPassword && (
        <CustomText variant={'error'}>{errors.confirmPassword.message}</CustomText>
      )}

      <CustomButton className="mt-2" onPress={handleSubmit(onSubmit)}>
        <CustomText className="font-alansans text-primary-foreground">Signup</CustomText>
      </CustomButton>

      <View className="flex w-full flex-row items-center justify-center">
        <View className="flex-1 border border-muted" />
        <CustomText className="px-2">OR</CustomText>
        <View className="flex-1 border border-muted" />
      </View>

      <CustomButton
        buttonVariant="muted"
        className="flex h-12 w-full flex-row items-center justify-center gap-4">
        <Ionicons name="logo-google" size={24} color="#e29478eb" />
        <CustomText className="font-alansans ">Connect with Google</CustomText>
      </CustomButton>

      <View className="mt-3 flex flex-row items-center justify-center">
        <CustomText className="font-lexend-light">Already have an Account? </CustomText>
        <CustomText
          onPress={() => OnAuthTypeChange('login')}
          variant="link"
          className="font-lexend-light">
          Go to Login
        </CustomText>
      </View>
    </View>
  );
};

export default SignupForm;
