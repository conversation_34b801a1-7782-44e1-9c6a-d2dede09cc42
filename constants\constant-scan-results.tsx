import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import { EatableInfoBoxProps } from '@/components/scan-and-check/eatable-info';
import { Macros } from '@/services/scan/types';
import { JSX } from 'react';

export type MACRO_ICON_PROPS = {
  [K in keyof Macros]: JSX.Element;
};

export const MACRO_ICONS: MACRO_ICON_PROPS = {
  protein_g: <FontAwesome6 name="dumbbell" size={18} color="#3B82F6" />,
  carbs_g: <FontAwesome6 name="bowl-rice" size={18} color="#F59E0B" />,
  fiber_g: <FontAwesome6 name="leaf" size={18} color="#8BC34A" />,
  fat_g: <FontAwesome6 name="burger" size={18} color="#F97316" />,
  saturated_fat_g: <FontAwesome6 name="cheese" size={18} color="#EAB308" />, // yellow (solid fat)
  unsaturated_fat_g: <FontAwesome6 name="cheese" size={18} color="#65A30D" />, // olive oil green
  sugar_g: <FontAwesome6 name="candy-cane" size={18} color="#EF4444" />, // red (sweet)
};

export const quantityValues = [
  {
    value: 100,
    label: '100',
  },
  {
    value: 200,
    label: '200',
  },
  {
    value: 300,
    label: '300',
  },
  {
    value: 400,
    label: '400',
  },
  {
    value: 500,
    label: '500',
  },
  {
    value: 600,
    label: '600',
  },
  {
    value: 700,
    label: '700',
  },
  {
    value: 800,
    label: '800',
  },
  {
    value: 900,
    label: '900',
  },
  {
    value: 1000,
    label: '1000',
  },
];

export const quantityMetrics = [
  {
    value: 'mg',
    label: 'Milligrams',
  },
  {
    value: 'g',
    label: 'Grams',
  },
];

// NOTE: FOLLOWING FROM HERE IS A TEMPORARY MOCK DATA

export const eatableResults: EatableInfoBoxProps = {
  type: 'success',
  title: 'Yes',
  message: 'This fits well within your daily nutritional goals. Enjoy!',
};

export const notEatableResults: EatableInfoBoxProps = {
  type: 'warning',
  title: 'No',
  message: 'Based on your weight loss goal, a portion size of 100g is recommended.',
};

// Food facts, quotes, and advice for loading screen
export const FOOD_FACTS = [
  {
    icon: 'apple-whole',
    text: 'An apple a day keeps the doctor away! Apples are rich in fiber and antioxidants.',
  },
  {
    icon: 'carrot',
    text: 'Carrots are packed with beta-carotene, which your body converts to vitamin A.',
  },
  {
    icon: 'fish',
    text: 'Fish is an excellent source of omega-3 fatty acids, great for brain health.',
  },
  {
    icon: 'leaf',
    text: 'Dark leafy greens are nutritional powerhouses loaded with vitamins and minerals.',
  },
  {
    icon: 'seedling',
    text: 'Eating a variety of colorful foods ensures you get diverse nutrients.',
  },
  {
    icon: 'bowl-food',
    text: 'Portion control is key - use smaller plates to help manage serving sizes.',
  },
  {
    icon: 'droplet',
    text: 'Stay hydrated! Your body is about 60% water and needs constant replenishment.',
  },
  {
    icon: 'clock',
    text: 'Eating slowly helps your brain register fullness and improves digestion.',
  },
  {
    icon: 'heart',
    text: 'Whole grains support heart health and provide sustained energy.',
  },
  {
    icon: 'dumbbell',
    text: 'Protein helps build and repair muscles. Include it in every meal.',
  },
  {
    icon: 'sun',
    text: 'Vitamin D from sunlight helps your body absorb calcium better.',
  },
  {
    icon: 'cheese',
    text: 'Calcium from dairy products helps build strong bones and teeth.',
  },
  {
    icon: 'pepper-hot',
    text: 'Spicy foods can boost metabolism and may help with weight management.',
  },
  {
    icon: 'cookie-bite',
    text: 'Everything in moderation - even treats can fit into a balanced diet.',
  },
  {
    icon: 'utensils',
    text: 'Mindful eating helps you enjoy food more and recognize hunger cues.',
  },
];
