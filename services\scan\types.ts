export interface ScanResponse {
  id: number;
  advice: string;
  brand: string | null | number;
  calories: string | null | number;
  food_name: string | null | number;
  is_eatable: string;
  macros: Macros;
  serving: Serving;
  source: string;
  image_url: string; // URL to the uploaded image
}

export interface Macros {
  carbs_g: string | null | number;
  fat_g: string | null | number;
  fiber_g: string | null | number;
  protein_g: string | null | number;
  saturated_fat_g: string | null | number;
  sugar_g: string | null | number;
  unsaturated_fat_g: string | null | number;
}

export interface Serving {
  quantity: string | null | number;
  unit: string | null | number;
  weight_grams: string | null | number;
}

export interface ScanRequest {
  imageUri: string; // Local file URI for multipart upload
}

// Scan History Types
export interface ScanHistoryItem {
  image_url: string; // URL to the uploaded image
  nutrition_label: {
    id: number;
    food_name: string;
    brand: string | null;
    serving: {
      unit: string;
      quantity: number;
      weight_grams: number;
    };
    calories: number;
    macros: Macros;
    source: string;
    is_eatable: string;
    advice: string;
  };
  is_logged: boolean;
  is_consumed: boolean;
  consumed_quantity?: number;
  consumed_metric?: string;
  consumed_nutrition?: {
    macros: Macros;
    serving: {
      unit: string;
      quantity: number;
      weight_grams: number;
    };
    calories: number;
  };
  logged_at?: string;
  created_at: string;
}

export interface ScanHistoryResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: ScanHistoryItem[];
}

// today summary

export interface TodaySummaryResponse {
  date: string;
  total_calories: number;
  macros: Macros;
}
